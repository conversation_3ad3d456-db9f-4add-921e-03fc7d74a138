package com.yuelan.hermes.quanyi.remote;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.common.pojo.properties.SoftGameProperties;
import com.yuelan.hermes.quanyi.remote.softgame.request.SoftGameOrderSubmitReq;
import com.yuelan.hermes.quanyi.remote.softgame.response.SoftGameBaseResponse;
import com.yuelan.hermes.quanyi.remote.softgame.response.SoftGameOrderQueryResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * 软游通v2管理器测试类
 *
 * <AUTHOR> 2025/7/31
 * @since 2025/7/31
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class SoftGameManagerTest {

    @Resource
    private SoftGameManager softGameManager;

    @Resource
    private SoftGameProperties softGameProperties;

    @Before
    public void setUp() {
        // 确保使用沙箱环境
        softGameProperties.setSandbox(true);
        log.info("当前环境配置: sandbox={}, businessId={}",
                softGameProperties.isSandbox(),
                softGameProperties.getCurrentBusinessId());
    }


    /**
     * 测试订单提交 - 成功场景
     */
    @Test
    public void testSubmitOrderSuccess() {
        log.info("=== 测试订单提交 - 成功场景 ===");

        SoftGameOrderSubmitReq req = buildTestOrderRequest();
        // 使用测试成功商品ID
        req.setGoodsId(softGameProperties.getSandboxConfig().getTestSuccessGoodsId());
        req.setGameName("测试返回成功");

        log.info("提交订单请求: {}", JSON.toJSONString(req));

        SoftGameBaseResponse<String> response = softGameManager.submitOrder(req, new StringBuilder(), new StringBuilder());

        log.info("订单提交响应: {}", JSON.toJSONString(response));

        // 验证结果
        if (response.isSuccess()) {
            log.info("✅ 订单提交成功（会异步通知成功）");
        } else {
            log.error("❌ 订单提交失败: {}", response.getMes());
        }
    }

    /**
     * 测试订单提交 - 失败场景
     */
    @Test
    public void testSubmitOrderFail() {
        log.info("=== 测试订单提交 - 失败场景 ===");

        SoftGameOrderSubmitReq req = buildTestOrderRequest();
        // 使用测试失败商品ID
        req.setGoodsId(softGameProperties.getSandboxConfig().getTestFailGoodsId());
        req.setGameName("测试返回失败");

        log.info("提交订单请求: {}", JSON.toJSONString(req));

        SoftGameBaseResponse<String> response = softGameManager.submitOrder(req, new StringBuilder(), new StringBuilder());

        log.info("订单提交响应: {}", JSON.toJSONString(response));

        // 验证结果
        if (!response.isSuccess()) {
            log.info("✅ 订单提交成功测试通过（会异步通知失败）: {}", response.getMes());
        }
    }

    /**
     * 测试订单提交 - 卡密场景
     */
    @Test
    public void testSubmitOrderCard() {
        log.info("=== 测试订单提交 - 卡密场景 ===");

        SoftGameOrderSubmitReq req = buildTestOrderRequest();
        // 使用测试卡密商品ID
        req.setGoodsId(softGameProperties.getSandboxConfig().getTestCardGoodsId());
        req.setGameName("卡密测试");
        req.setGameType("卡密");

        log.info("提交订单请求: {}", JSON.toJSONString(req));

        SoftGameBaseResponse<String> response = softGameManager.submitOrder(req, new StringBuilder(), new StringBuilder());

        log.info("订单提交响应: {}", JSON.toJSONString(response));

        // 验证结果
        if (response.isSuccess()) {
            log.info("✅ 卡密订单提交成功");

            // 等待一段时间后查询订单状态
            try {
                Thread.sleep(3000); // 等待3秒
                testQueryOrder(req.getUserOrderId());
            } catch (InterruptedException e) {
                log.warn("等待中断", e);
            }
        } else {
            log.error("❌ 卡密订单提交失败: {}", response.getMes());
        }
    }

    /**
     * 测试订单查询
     */
    @Test
    public void testQueryOrder() {
        testQueryOrder("TEST_ORDER_9fde2633348b4de3baea7b2ad006033a");
    }

    /**
     * 查询指定订单
     */
    private void testQueryOrder(String userOrderId) {
        log.info("=== 测试订单查询: {} ===", userOrderId);

        SoftGameBaseResponse<SoftGameOrderQueryResp> response =
                softGameManager.queryOrder(userOrderId);

        log.info("订单查询响应: {}", JSON.toJSONString(response));

        if (response.isSuccess() && response.getData() != null) {
            SoftGameOrderQueryResp orderInfo = response.getData();
            log.info("✅ 订单查询成功");
            log.info("订单状态: {}", orderInfo.getStatus());
            log.info("状态说明: {}", orderInfo.getMes());

            if (orderInfo.isFinalStatus()) {
                if (orderInfo.isSuccess()) {
                    log.info("🎉 订单充值成功! 结算金额: {}", orderInfo.getPayoffPriceTotal());
                } else {
                    log.warn("💔 订单充值失败: {}", orderInfo.getMes());
                }
            } else {
                log.info("⏳ 订单处理中...");
            }
        } else {
            log.error("❌ 订单查询失败");
        }
    }

    /**
     * 构建测试订单请求
     */
    private SoftGameOrderSubmitReq buildTestOrderRequest() {
        SoftGameOrderSubmitReq req = new SoftGameOrderSubmitReq();
        req.setUserOrderId("TEST_ORDER_" + UUID.randomUUID().toString().replace("-", ""));
        req.setUserName("18966835609"); // 沙箱测试手机号
        req.setGoodsNum("1"); // 面值1元
        req.setNoticeUrl("http://test.example.com/softgame-v2/notify"); // 测试回调地址
        req.setGameType("直充");
        req.setAcctType("手机号");
        req.setOrderIp(null);

        return req;
    }

    /**
     * 批量测试 - 模拟并发场景
     */
    @Test
    public void testBatchSubmitOrders() {
        log.info("=== 批量测试订单提交 ===");

        String[] testTypes = {"success", "fail", "card"};

        for (String testType : testTypes) {
            try {
                SoftGameOrderSubmitReq req = buildTestOrderRequest();

                switch (testType) {
                    case "success":
                        req.setGoodsId(softGameProperties.getSandboxConfig().getTestSuccessGoodsId());
                        req.setGameName("批量测试-成功");
                        break;
                    case "fail":
                        req.setGoodsId(softGameProperties.getSandboxConfig().getTestFailGoodsId());
                        req.setGameName("批量测试-失败");
                        break;
                    case "card":
                        req.setGoodsId(softGameProperties.getSandboxConfig().getTestCardGoodsId());
                        req.setGameName("批量测试-卡密");
                        break;
                }

                log.info("提交{}类型订单: {}", testType, req.getUserOrderId());
                SoftGameBaseResponse<String> response = softGameManager.submitOrder(req, new StringBuilder(), new StringBuilder());
                log.info("{}类型订单结果: {}", testType, response.isSuccess() ? "成功" : "失败");

                // 间隔1秒避免频率限制
                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("批量测试异常: {}", testType, e);
            }
        }
    }

    /**
     * 测试签名验证
     */
    @Test
    public void testSignatureValidation() {
        log.info("=== 测试签名验证 ===");

        SoftGameOrderSubmitReq req = buildTestOrderRequest();
        req.setGoodsId(softGameProperties.getSandboxConfig().getTestSuccessGoodsId());
        req.setGameName("签名测试");

        // 正常提交，验证签名是否正确
        SoftGameBaseResponse<String> response = softGameManager.submitOrder(req, new StringBuilder(), new StringBuilder());

        if (response.isSuccess()) {
            log.info("✅ 签名验证通过");
        } else if ("02".equals(response.getResult())) {
            log.warn("⚠️ 可能是签名错误或其他参数问题: {}", response.getMes());
        }
    }
}
