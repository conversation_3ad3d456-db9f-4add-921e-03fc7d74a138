package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.util.RandomUtil;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.common.enums.HuBeiShuKePayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.properties.HuBeiShuKeEduProperties;
import com.yuelan.hermes.quanyi.remote.benefit.HuBeiShuKeEduManager;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduOrderConfirmReq;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduSmsCodeReq;
import com.yuelan.hermes.quanyi.remote.hbsk.response.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class HuBeiShuKeEduManagerTest {

    @Autowired
    private HuBeiShuKeEduManager huBeiShuKeEduManager;
    @Autowired
    private HuBeiShuKeEduProperties huBeiShuKeEduProperties;

    @Test
    public void testGetSmsCode() throws InterruptedException {
        HuBeiShuKeEduProperties.EduConf eduConf = HuBeiShuKeEduProperties.pkgConfigMap.get(HuBeiShuKePayPkgEnum.EDU_BY_THUMB);


        ActivityInfoResp activityInfo = huBeiShuKeEduManager.getActivityInfo(eduConf.getActivityId());
        if (!activityInfo.isBizSuccess()) {
            log.error("获取活动信息失败: {}", activityInfo.getText());
            return;
        }

        ActivityDetail activityDetail = activityInfo.getExt();
        ActivityGood activityGood = activityDetail.getGoods().get(0);

        // String mobile = "15347232370";

        String mobile = "13989458839";
        TelecomCheckResp telecomCheckResp = huBeiShuKeEduManager.checkIsTelecomWithResponse(mobile);
        if (!telecomCheckResp.isBizSuccess()) {
            log.error("检查手机号是否为电信用户失败: {}", telecomCheckResp.getExt());
            return;
        }
        //{
        //     "code": 200,
        //     "text": "success",
        //     "ext": {
        //         "activityId": 1034,
        //         "activityName": "订大拇哥精品教育权益包",
        //         "beginTimes": 1737684495000,
        //         "endTimes": 1893427199000,
        //         "activityType": 1,
        //         "description": "<p>活动规则</p><p>1、2024.2.01-2026.12.31,湖北省内电信用户通过本页面参与活动。订购“大拇哥精品教育权益包”产品，资费20元/月，连续包月。开通当日全额收费并下发当月所有权益。次月续订全额收费，退订当月正常收费。</p><p>2、大拇哥乐园会员权益内容以自然月为有效期（如用户1月20日订购，会员权益2月1日到期；2月1日订购，3月1日到期，以此类推）。</p><p>3、退订方式：用户可拨打10000号或4008689689办理退订，取消后大拇哥乐园会员权益包次月生效，教育类会员权益领取截止到退订当月月底。</p><p>4、对活动如有任何疑问，可拨打10000或客服热线4008689689咨询，客服时间：周一至周日 9:00-22:00。</p>",
        //         "resultText": "<p>订购成功后，您可以通过以下方式领取权益：</p><p>1、点击<a href=\"https://act.play.cn/hd/p/gather/\" target=\"_blank\">https://act.play.cn/hd/p/gather/</a>领取</p><p>2、前往“天翼云游戏”公众号-“热门活动”-“权益领取”领取</p><p>3、下载天翼云游戏客户端，点击“头像”-“权益”领取，客户端下载：</p><p><a href=\"https://h5.play.cn/cdl?caf=35050964\" target=\"_blank\">https://h5.play.cn/cdl?caf=35050964</a></p>",
        //         "moduleId": null,
        //         "captchaSwitch": 0,
        //         "popFrameSwitch": 0,
        //         "popFrameUrl": "",
        //         "status": 1,
        //         "protocolTitle": "用户协议",
        //         "protocolUrl": "https://act.play.cn/hd/t/agreement/#/guide_detail?content_id=14011",
        //         "protocolSwitch": 1,
        //         "backgroundColor": "#295fe3",
        //         "materials": {
        //             "verticalBaseImgUrl": "https://cmscdn.play.cn/f/cloud/www/img/20250124/1737684534431.jpg"
        //         },
        //         "goods": [
        //             {
        //                 "goodId": 53,
        //                 "goodName": "大拇哥精品教育权益包",
        //                 "goodDesc": "20元/月，连续包月。每月享大拇哥乐园会员权益（端内无广告无内购；家长管控功能；护眼模式；大中小屏多终端适配）",
        //                 "saleProductId": "1834157369538383897",
        //                 "saleProductName": "大拇哥精品教育权益包",
        //                 "saleProductPrice": "2000",
        //                 "activityId": 1034,
        //                 "orderX": 0
        //             }
        //         ],
        //         "labels": []
        //     }
        // }


        //{
        //     "phone": "15347232370",
        //     "app_id": "1882670132648480821",
        //     "sale_product_id": "1834157369538383897",
        //     "source_id": 1034,
        //     "sign": "2d6eea096299ead1834cd9ea82d8c199",
        //     "province_pay_type": ""
        // }
        EduSmsCodeReq req = new EduSmsCodeReq();
        req.setPhone(mobile);
        req.setApp_id(eduConf.getAppId());
        req.setSale_product_id(activityGood.getSaleProductId());
        req.setSource_id(activityDetail.getActivityId());
        req.setProvince_pay_type("");

        EduSmsCodeResp eduSmsCodeResp = huBeiShuKeEduManager.sendSmsCode(req);
        log.info("发送短信验证码结果: {}", eduSmsCodeResp);

        log.info("随机生成验证码中: ");
        Thread.sleep(5000);
        String smsCode = RandomUtil.randomNumbers(6);
        log.info("输入的短信验证码: {}", smsCode);

        EduOrderConfirmReq confirmReq = new EduOrderConfirmReq();
        confirmReq.setPhone(huBeiShuKeEduManager.confirmOrderEncryptPhone(mobile));
        confirmReq.setApp_id(eduConf.getAppId());
        confirmReq.setSource_id(activityDetail.getActivityId());
        confirmReq.setSource_name(activityDetail.getActivityName());
        confirmReq.setSale_product_id(activityGood.getSaleProductId());
        confirmReq.setUa(HuBeiShuKeEduManager.USER_AGENT);
        confirmReq.setDevice_info(HuBeiShuKeEduManager.DEVICE_INFO);
        confirmReq.setSms_code(smsCode);
        confirmReq.setTime_stamp(System.currentTimeMillis());
        confirmReq.setCp_channel_code(eduConf.getCpChannelCode());
        confirmReq.setApp_name("");
        confirmReq.setProvince_pay_type("");
        huBeiShuKeEduManager.confirmOrder(confirmReq);


    }


}
