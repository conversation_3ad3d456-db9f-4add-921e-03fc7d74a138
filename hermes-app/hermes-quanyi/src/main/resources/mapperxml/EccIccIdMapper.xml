<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccIccIdMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccIccIdDO">
    <!--@mbg.generated-->
    <!--@Table ecc_iccid-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="iccid" jdbcType="VARCHAR" property="iccId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="channel_type" jdbcType="INTEGER" property="channelType" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="validity_period" jdbcType="DATE" property="validityPeriod" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id,
    iccid,
    product_id,
    channel_type,
    channel_id,
    batch_id,
    validity_period,
    create_time,
    update_time
  </sql>

  <insert id="saveBatchIgnore">
    INSERT INTO ecc_iccid
      (<include refid="Base_Column_List" />)
    VALUES
      <foreach collection="list" item="item" separator=",">
        (
          #{item.id},
          #{item.iccId},
          #{item.productId},
          #{item.channelType},
          #{item.channelId},
          #{item.batchId},
          #{item.validityPeriod},
          now(),
          #{item.updateTime}
        )
      </foreach>
    ON DUPLICATE KEY UPDATE
    update_time = now()
  </insert>
</mapper>
