package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAdminChannelDO;

/**
 * <AUTHOR> 2024/6/7 上午10:57
 */
public interface EccAdminChannelDOMapper extends BaseMapper<EccAdminChannelDO> {

    default EccAdminChannelDO getByAdminId(Long adminId) {
        return selectOne(Wrappers.<EccAdminChannelDO>lambdaQuery().eq(EccAdminChannelDO::getAdminId, adminId));
    }
}