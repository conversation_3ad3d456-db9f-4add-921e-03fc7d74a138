package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualStockItemDO;
import com.yuelan.hermes.quanyi.controller.request.GoodsVirtualStockDetailReq;
import com.yuelan.hermes.quanyi.controller.response.GoodsVirtualStockDetailRsp;
import com.yuelan.hermes.quanyi.controller.response.VoucherPasswordRsp;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface GoodsVirtualStockItemMapper extends BaseMapper<GoodsVirtualStockItemDO> {
    int batchInsert(@Param("list") List<GoodsVirtualStockItemDO> list);

    Long countByGoodsVirtualStockDetailReq(@Param("req") GoodsVirtualStockDetailReq req);

    List<GoodsVirtualStockDetailRsp> pageByGoodsVirtualStockDetailReq(@Param("req") GoodsVirtualStockDetailReq req);

    List<GoodsVirtualStockDetailRsp> cursorByGoodsVirtualStockDetailReq(@Param("req") GoodsVirtualStockDetailReq req, @Param("maxId") Long maxId);

    Long getVoucherPasswordCount(@Param("req") GoodsVirtualStockDetailReq req);

    List<VoucherPasswordRsp> getVoucherPasswordList(@Param("req") GoodsVirtualStockDetailReq req);

    List<VoucherPasswordRsp> voucherPasswordListExport(@Param("req") GoodsVirtualStockDetailReq req, @Param("maxId") Long maxId);

    GoodsVirtualStockItemDO findOne(@Param("id") Long id);

    int updateVoucherPasswordStatus(@Param("id") Long id, @Param("tarEnable") int tarEnable, @Param("orgEnable") int orgEnable);

    GoodsVirtualStockItemDO checkVoucherCodePassword(@Param("qrCodeUrl") String qrCodeUrl, @Param("voucherCode") String voucherCode,
                                                     @Param("voucherPassword") String voucherPassword, @Param("skuId") Long skuId);

    /**
     * 扣减库存
     */
    int deduction(@Param("billNo") String billNo, @Param("buyer") String buyer, @Param("salePrice") BigDecimal salePrice,
                  @Param("skuId") Long skuId, @Param("limit") Long limit);

    /**
     * 根据单号查询卡密
     */
    List<GoodsVirtualStockItemDO> findBySkuIdAndBillNo(@Param("skuId") Long skuId, @Param("billNo") String billNo);

    Integer statisticsWaitExpiresAmountBySkuId(@Param("skuId") Long skuId, @Param("expireDate") Date expireDate);

    Integer statisticsExpiresAmountBySkuId(@Param("skuId") Long skuId);

    Integer statisticsRealAmountBySkuId(@Param("skuId") Long skuId);

    Integer statisticsSellAmountBySkuId(@Param("skuId") Long skuId);

    Integer statisticsDisableAmountBySkuId(@Param("skuId") Long skuId);

    Integer statisticsExpiresAmountByGoodsVirtualStockDetailReq(@Param("req") GoodsVirtualStockDetailReq req);

    int countInStockByIdIn(@Param("ids") Collection<Long> ids);

    int deleteInStockByIdIn(@Param("ids") Collection<Long> ids);
}