package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.MiGuProxyPayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.properties.MiGuProxyProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2025/5/10
 * @since 2025/5/10
 */
@Slf4j
@Service
@AllArgsConstructor
public class MiGuProxyManager {
    private static final String UA = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36";
    private static final String SMS_REQ_URL = "http://dh.new-game.cn//channel/getCode";
    private static final String SMS_VERIFY_URL = "http://dh.new-game.cn//channel/submitCode";


    private final MiGuProxyProperties miGuProxyProperties;
    private final BenefitOrderLogService benefitOrderLogService;

    /**
     * 发送短信验证码
     *
     * @param mobile 手机号
     * @return SmsCodeResp
     */
    public SmsCodeResp sendSmsCode(String orderNo, String mobile, MiGuProxyPayPkgEnum miGuProxyPayPkgEnum) {
        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, mobile);

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(SMS_REQ_URL)
                .contentType(ContentType.JSON.getValue())
                .header("User-Agent", UA)
                .body(buildSmsReqBody(miGuProxyPayPkgEnum, orderNo, mobile))
                .log(BenefitPayChannelEnum.MG_QG_PROXY, args);
        HttpResponse resp = benefitOrderLogService.http(requestWrapper);
        return JSON.parseObject(resp.body(), SmsCodeResp.class);
    }

    private String buildSmsReqBody(MiGuProxyPayPkgEnum miGuProxyPayPkgEnum, String orderNo, String mobile) {
        MiGuProxyProperties.ProdConfig prodConfig = miGuProxyProperties.getProdConfigMap().get(miGuProxyPayPkgEnum);
        String prodCode = prodConfig.getProdCode();
        // a_oId	String	是		渠道订单号
        // phone	String	是		手机号
        // source	String	是		对接唯一值
        // package	String	是	package和packageName必须填一项	包名 com.xxx.xxx
        // packageName	String	是	package和packageName必须填一项	包名 中文
        // user_ip	String	是		用户IP ***********
        // userAgent	String	可选	header中不能传递的，urlencode之后传入该参数	用户的UserAgent
        JSONObject params = new JSONObject() {{
            put("a_oId", orderNo);
            put("phone", mobile);
            put("source", prodCode);
            put("package", "com.yuelan.app");
            put("packageName", "yuelan");
            put("user_ip", "127.0.0.1");
        }};
        return params.toString();
    }


    /**
     * 验证验证码
     *
     * @param phone      手机
     * @param orderNo    我方系统的订单号
     * @param smsCode    验证码
     * @param smsOrderNo 获取验证码时候的点多
     */
    public SmsVerifyResp verifySmsCode(String phone, String smsCode, String orderNo, String smsOrderNo, MiGuProxyPayPkgEnum miGuProxyPayPkgEnum) {
        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_CHECK, phone, orderNo);

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(SMS_VERIFY_URL)
                .contentType(ContentType.JSON.getValue())
                .header("User-Agent", UA)
                .body(buildSmsVerifyReqBody(miGuProxyPayPkgEnum, phone, smsCode, smsOrderNo))
                .log(BenefitPayChannelEnum.MG_QG_PROXY, args);
        HttpResponse resp = benefitOrderLogService.http(requestWrapper);
        return JSON.parseObject(resp.body(), SmsVerifyResp.class);

    }

    private String buildSmsVerifyReqBody(MiGuProxyPayPkgEnum miGuProxyPayPkgEnum, String phone, String smsCode, String smsOrderNo) {
        //{
        //   	"a_oId":"testorder123",
        //   	"phone":"13188888888",
        //   	"smsCode":"123456",
        //   	"source":"test"
        // }
        MiGuProxyProperties.ProdConfig prodConfig = miGuProxyProperties.getProdConfigMap().get(miGuProxyPayPkgEnum);
        String prodCode = prodConfig.getProdCode();
        JSONObject params = new JSONObject() {{
            put("a_oId", smsOrderNo);
            put("phone", phone);
            put("smsCode", smsCode);
            put("source", prodCode);
        }};
        return params.toString();
    }

    /**
     * 获取短信验证码response
     * {
     * "success": true,
     * "desc": "",
     * "data": {"orderid":"1864958543574769665"}
     * }
     */
    @Data
    public static class SmsCodeResp {
        private boolean success;
        private String desc;
        private ResData data;

        @Data
        public static class ResData {
            private String orderid;
        }

    }

    /**
     * {
     * "success": true,
     * "desc": "",
     * "data": null
     * }
     */
    @Data
    public static class SmsVerifyResp {
        private boolean success;
        private String desc;
        private String data;
    }

}
