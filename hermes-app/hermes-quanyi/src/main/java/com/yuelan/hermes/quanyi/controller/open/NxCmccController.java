package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.remote.NxCmcc2Manager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "宁夏移动API")
@RequestMapping("/nxcmcc")
@SaIgnore
@RestController
public class NxCmccController {

    @Autowired
    private NxCmcc2Manager nxCmcc2Manager;

    @Operation(summary = "订单回调通知")
    @PostMapping(value = "/order/notify")
    public String notify(@RequestBody String body, HttpServletRequest request) {

        log.info("宁夏移动回调 version:{} appId:{} seqId:{} timestamp:{} signature:{}", request.getHeader("version"), request.getHeader("appId"),
                request.getHeader("seqId"), request.getHeader("timestamp"), request.getHeader("signature"));

        return nxCmcc2Manager.orderNotify(body);
    }

}
