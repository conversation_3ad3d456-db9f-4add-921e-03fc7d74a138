package com.yuelan.hermes.quanyi.remote.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2025/5/27
 * @since 2025/5/27
 */
@Data
public class SkuSaveOrUpdateReq {

    @Schema(description = "权益平台的productId")
    private Long productId;

    @Schema(description = "权益平台的productCode")
    private String productCode;

    @Schema(description = "产品名字和商品名字")
    private String productName;

    @Schema(description = "权益平台的distributionUrl")
    private String distributionUrl;

    @Schema(description = "运营商")
    private Integer operator;

    @Schema(description = "运营商产品名字")
    private String spProductName;

    @Schema(description = "上下架状态：0-下架；1-上架")
    private Integer status;

    public boolean isNormal() {
        return status != null && status == 1;
    }
}
