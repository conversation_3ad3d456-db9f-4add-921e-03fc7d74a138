package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;

/**
 * <AUTHOR> 2024/12/23
 * @since 2024/12/23
 */
@Data
public class KeDangCallbackDataReq {

    /**
     * 订单号 提交订单返回的订单号
     */
    private String orderNumber;

    private String choosePhone;
    /**
     * 值	说明
     * 1	认证状态（再通过mStatus判断认证是否通过）只需要判断mStatus=2时的认证失败就行，但失败之后，后续有概率重新进入生产，等type=7的回调
     * 2	已发货（再通过logisticsCompany字段获取物流信息）
     * 3	已激活
     * 4	异常订单（7天内有概率人工介入使订单恢复正常，等type=6的回调）
     * 7    天后还一直卡在异常状态，则可自行视为废单
     * 5	首充
     * 6	异常订单恢复正常
     * 7	订单进入生产
     */
    private Integer type;
    /**
     * type=1时才需要用到此字段
     * 1：认证通过
     * 2：认证失败
     */
    private String mStatus;

    private String logisticsCompany;

    private String logisticsCompanyCode;
    /**
     * 发货时间（为空代表接口调用时间）
     */
    private String deliverTime;

    /**
     * activeTime 激活时间（为空代表接口调用时间）
     */
    private String activeTime;

    /**
     * 异常原因描述type=4时才需要用到
     */
    private String info;
    /**
     * 首充金额 元
     */
    private String firstFlushMoney;
    /**
     * 首充时间 (为空代表接口调用时间)
     */
    private String firstFlushTime;

}
