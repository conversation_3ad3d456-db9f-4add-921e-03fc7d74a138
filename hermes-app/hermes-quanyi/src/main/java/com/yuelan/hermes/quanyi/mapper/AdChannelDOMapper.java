package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.AdChannelDO;

import java.util.List;

/**
 * <AUTHOR> 2024/6/18 下午3:35
 */
public interface AdChannelDOMapper extends BaseMapper<AdChannelDO> {

    default List<AdChannelDO> listByModuleType(Integer moduleType) {
        return selectList(Wrappers.<AdChannelDO>lambdaQuery().eq(AdChannelDO::getModuleType, moduleType)
                .orderByDesc(AdChannelDO::getAdChannelId));
    }

}