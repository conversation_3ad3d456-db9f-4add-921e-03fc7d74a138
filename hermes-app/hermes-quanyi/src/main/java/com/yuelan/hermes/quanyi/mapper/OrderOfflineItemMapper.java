package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.OrderOfflineItemDO;
import com.yuelan.hermes.quanyi.controller.request.OrderOfflineBatchExportReq;
import com.yuelan.hermes.quanyi.controller.request.OrderOfflineExportReq;
import com.yuelan.hermes.quanyi.controller.request.OrderOfflineItemListReq;
import com.yuelan.hermes.quanyi.controller.response.OrderOfflineItemRsp;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OrderOfflineItemMapper extends BaseMapper<OrderOfflineItemDO> {
    int batchInsert(@Param("list") List<OrderOfflineItemDO> list);

    List<OrderOfflineItemRsp> cursorByOrderOfflineListReq(@Param("req") OrderOfflineExportReq req, @Param("maxId") Long maxId);

    List<OrderOfflineItemRsp> cursorByOrderOfflineBatchExportReq(@Param("req") OrderOfflineBatchExportReq req, @Param("maxId") Long maxId);

    List<OrderOfflineItemDO> pageByOrderOfflineItemListReq(OrderOfflineItemListReq req);

    Long countByOrderOfflineItemListReq(OrderOfflineItemListReq req);

    OrderOfflineItemDO findByItemNo(String itemNo);

    List<OrderOfflineItemDO> findByOrderNo(String orderNo);

    /**
     * 更新供应商订单号
     *
     * @param id              订单明细ID
     * @param supplierOrderNo 供应商订单号
     */
    int updateSupplierOrderNo(@Param("id") Long id, @Param("supplierOrderNo") String supplierOrderNo);

    int updateItemDO(OrderOfflineItemDO orderOfflineItemDO);

    int updateSms(@Param("id") Long id, @Param("sms_status") Integer smsStatus, @Param("sendTime") Date sendTime);
}