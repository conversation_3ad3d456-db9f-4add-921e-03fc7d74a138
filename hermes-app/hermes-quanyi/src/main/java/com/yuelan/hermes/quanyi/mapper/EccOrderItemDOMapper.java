package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOrderItemDO;

import java.util.List;

/**
 * <AUTHOR> 2024/5/15 下午5:38
 */
public interface EccOrderItemDOMapper extends BaseMapper<EccOrderItemDO> {
    /**
     * 创建月表
     *
     * @param monthTime 月份 yyyyMM
     */
    void createMonthTableIfNotExists(String monthTime);

    /**
     * 通过主订单号查询list
     *
     * @param orderId 主订单号
     */
    default List<EccOrderItemDO> listByOrderId(Long orderId) {
        return selectList(Wrappers.<EccOrderItemDO>lambdaQuery()
                .eq(EccOrderItemDO::getOrderId, orderId));
    }
}