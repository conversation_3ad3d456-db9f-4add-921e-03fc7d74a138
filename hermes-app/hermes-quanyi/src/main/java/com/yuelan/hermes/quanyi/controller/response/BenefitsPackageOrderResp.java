package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.yuelan.core.util.MapstructUtils;
import com.yuelan.hermes.commons.excel.LocalDateTimeConverter;
import com.yuelan.hermes.quanyi.common.enums.PageBenefitsPackageStatusEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderExtensionDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.BooleanConverter;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.YesOrNoConverter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> 2025/4/28
 * @since 2025/4/28
 */
@ContentRowHeight(25)
@HeadRowHeight(35)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMapper(target = BenefitsPackageOrderDO.class)
public class BenefitsPackageOrderResp extends BenefitsOrderExtensionResp {

    @ColumnWidth(20)
    @ExcelProperty("订单id")
    @Schema(description = "订单id")
    private Long packageOrderId;

    @ColumnWidth(20)
    @ExcelProperty("权益包id")
    @Schema(description = "权益包id")
    private Long packageId;

    @ColumnWidth(20)
    @ExcelProperty("权益包名")
    @Schema(description = "权益包名")
    private String packageName;

    @ColumnWidth(20)
    @ExcelProperty("权益包编码")
    @Schema(description = "权益包编码")
    private String packageCode;

    @ColumnWidth(20)
    @ExcelProperty("手机号码")
    @Schema(description = "手机号码")
    private String mobile;

    @ColumnWidth(20)
    @ExcelProperty("销售价格")
    @Schema(description = "销售价格")
    @AutoMapping(ignore = true)
    @ReverseAutoMapping(ignore = true)
    private BigDecimal sellingPrice;

    @ExcelIgnore
    @Schema(description = "可选发集合数量")
    private Integer optionalCount;

    @ColumnWidth(20)
    @ExcelProperty("可选发发放成功的数量")
    @Schema(description = "可选发发放成功的数量")
    private Integer optionalDispatchCount;

    @ColumnWidth(20)
    @ExcelProperty("应该即时发放的权益数量")
    @Schema(description = "应该即时发放的权益数量")
    private Integer instantCount;

    @ColumnWidth(20)
    @ExcelProperty("即时发放成功的数量")
    @Schema(description = "即时发放成功的数量")
    private Integer instantDispatchCount;

    @ColumnWidth(20)
    @ExcelProperty("最多可选数量")
    @Schema(description = "最多可选数量")
    private Integer maxSelectable;

    @ColumnWidth(20)
    @ExcelProperty("发放失败的数量")
    @Schema(description = "发放失败的数量")
    private Integer dispatchErrorCount;

    @ColumnWidth(20)
    @ExcelProperty(value = "兑换截止时间", converter = LocalDateTimeConverter.class)
    @Schema(description = "兑换截止时间")
    private LocalDateTime redemptionDeadline;

    @ColumnWidth(20)
    @ExcelProperty("发放进度")
    @Schema(description = "发放进度")
    private String dispatchProgress;

    @ColumnWidth(20)
    @ExcelProperty(value = "退款状态", converter = YesOrNoConverter.class)
    @Schema(description = "退款状态: 0-未退款 1-已退款")
    private Integer isRefunded;


    @ColumnWidth(20)
    @ExcelProperty(value = "是否有效", converter = YesOrNoConverter.class)
    @Schema(description = "是否有效 0-失效 1-有效")
    private Integer isValid;

    @ColumnWidth(20)
    @ExcelProperty("领取状态")
    @Schema(description = "0-未领取：一个都没领   1-已领取：权益包内商品已领取完毕 2-领取中：权益包内商品未领取完毕  3-领取异常：权益包内商品领取失败")
    private Integer status;

    @ColumnWidth(20)
    @ExcelProperty(value = "是否已过期", converter = BooleanConverter.class)
    @Schema(description = "是否已过期")
    private Boolean isExpired;

    @ColumnWidth(20)
    @ExcelProperty(value = "退单时间", converter = LocalDateTimeConverter.class)
    @Schema(description = "退单时间")
    private LocalDateTime refundedTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "创建时间", converter = LocalDateTimeConverter.class)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "更新时间", converter = LocalDateTimeConverter.class)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


    public static BenefitsPackageOrderResp buildResp(BenefitsPackageOrderDO packageOrderDO) {
        if (Objects.isNull(packageOrderDO)) {
            return null;
        }
        BenefitsPackageOrderResp resp = MapstructUtils.convertNotNull(packageOrderDO, BenefitsPackageOrderResp.class);
        if (packageOrderDO.getSellingPrice() != null) {
            resp.setSellingPrice(BigDecimal.valueOf(packageOrderDO.getSellingPrice() / 100));
        }
        PageBenefitsPackageStatusEnum statusEnum = getPageBenefitsPackageStatusEnum(packageOrderDO);
        if (statusEnum != null) {
            resp.setStatus(statusEnum.getCode());
        }
        if (packageOrderDO.getOrderExtension() != null) {
            BenefitOrderExtensionDO orderExtension = packageOrderDO.getOrderExtension();
            resp.setExtensionInfo(orderExtension);
        }
        // 发放的进度  已经发放的数量/总的数量
        String dispatchProgress = (packageOrderDO.getInstantDispatchCount() + packageOrderDO.getOptionalDispatchCount()) + "/" + (packageOrderDO.getInstantCount() + packageOrderDO.getMaxSelectable());
        resp.setDispatchProgress(dispatchProgress);
        boolean isExpired = packageOrderDO.getRedemptionDeadline().isBefore(LocalDateTime.now());
        resp.setIsExpired(isExpired);
        return resp;
    }

    @Nullable
    private static PageBenefitsPackageStatusEnum getPageBenefitsPackageStatusEnum(BenefitsPackageOrderDO packageOrderDO) {
        PageBenefitsPackageStatusEnum statusEnum = null;
        int dispatchCount = packageOrderDO.getInstantDispatchCount() + packageOrderDO.getOptionalDispatchCount();
        int totalCount = packageOrderDO.getInstantCount() + packageOrderDO.getMaxSelectable();
        if (dispatchCount < totalCount) {
            statusEnum = PageBenefitsPackageStatusEnum.REDEEMING;
        } else if (dispatchCount == totalCount) {
            statusEnum = PageBenefitsPackageStatusEnum.REDEEMED;
        } else if (dispatchCount == 0) {
            statusEnum = PageBenefitsPackageStatusEnum.UNREDEEMED;
        }
        if (packageOrderDO.getDispatchErrorCount() > 0) {
            statusEnum = PageBenefitsPackageStatusEnum.REDEEM_ERROR;
        }
        return statusEnum;
    }
}
