package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccTaoBaoUserDO;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/5/9 下午4:14
 */
public interface EccTaoBaoUserDOMapper extends BaseMapper<EccTaoBaoUserDO> {
    default EccTaoBaoUserDO selectByTaoBaoUserId(String taobaoUserId) {
        return selectOne(Wrappers.<EccTaoBaoUserDO>lambdaQuery().eq(EccTaoBaoUserDO::getTaobaoUserId, taobaoUserId));
    }

    default boolean updatePhoneById(Long eccUserId, String phone) {
        return update(Wrappers.<EccTaoBaoUserDO>lambdaUpdate().eq(EccTaoBaoUserDO::getUserId, eccUserId)
                .set(EccTaoBaoUserDO::getBindPhone, phone)
                .set(EccTaoBaoUserDO::getBindTime, LocalDateTime.now())) > 0;
    }

    default EccTaoBaoUserDO selectByPhone(String phone) {
        return selectOne(Wrappers.<EccTaoBaoUserDO>lambdaQuery().eq(EccTaoBaoUserDO::getBindPhone, phone));
    }
}