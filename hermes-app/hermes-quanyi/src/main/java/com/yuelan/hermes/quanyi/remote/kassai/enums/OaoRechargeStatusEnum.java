package com.yuelan.hermes.quanyi.remote.kassai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OAO充值状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/09
 */
@Getter
@AllArgsConstructor
public enum OaoRechargeStatusEnum {

    /**
     * 否
     */
    NO(0, "否"),

    /**
     * 是
     */
    YES(1, "是");

    private final Integer code;
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static OaoRechargeStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OaoRechargeStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为OAO充值
     *
     * @return true表示是OAO充值
     */
    public boolean isOaoRecharge() {
        return this == YES;
    }
}
