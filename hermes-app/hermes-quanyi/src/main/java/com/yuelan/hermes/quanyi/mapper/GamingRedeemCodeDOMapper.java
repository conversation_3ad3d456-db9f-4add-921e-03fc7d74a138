package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingRedeemCodeDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2024/7/11 下午7:32
 */
public interface GamingRedeemCodeDOMapper extends MPJBaseMapper<GamingRedeemCodeDO> {
    /**
     * 更新一张兑换码的使用状态 满足商品id和未过期
     */
    int updateOneUsedByGoodsIdAndValidity(@Param("itemNo") String itemNo, @Param("goodsId") Long goodsId);

    default GamingRedeemCodeDO selectByItemNo(String itemNo) {
        return selectOne(Wrappers.<GamingRedeemCodeDO>lambdaQuery()
                .eq(GamingRedeemCodeDO::getItemNo, itemNo));
    }

    /**
     * 批量插入兑换码 忽略唯一索引冲突
     */
    int ignoreUniqueBatchInsert(@Param("list") List<GamingRedeemCodeDO> redeemCodeDOList);
}