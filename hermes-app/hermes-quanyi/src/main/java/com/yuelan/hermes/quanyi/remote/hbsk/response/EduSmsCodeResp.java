package com.yuelan.hermes.quanyi.remote.hbsk.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Objects;

/**
 * 教育包短信验证码响应
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class EduSmsCodeResp {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String text;

    /**
     * 扩展字段
     */
    private Boolean ext;

    /**
     * 判断是否成功
     */
    @JSONField(serialize = false)
    public boolean isBizSuccess() {
        return code != null && code == 200 && Objects.equals(ext, true);
    }


}
