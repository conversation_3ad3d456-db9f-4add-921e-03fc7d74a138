package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.github.yulichang.toolkit.LambdaUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuelan.core.util.MapstructUtils;
import com.yuelan.hermes.quanyi.common.enums.PageBenefitsPackageStatusEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderExtensionDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageOrderDO;
import com.yuelan.result.entity.PageRequest;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2025/4/28
 * @since 2025/4/28
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMapper(target = BenefitsPackageOrderDO.class)
public class BenefitsPackageOrderReq extends PageRequest {


    @Schema(description = "权益包订单id")
    private Long packageOrderId;

    @Schema(description = "权益包id")
    private Long packageId;

    @TableField(value = "package_name")
    private String packageName;

    @Schema(description = "权益包编码")
    private String packageCode;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "兑换截止时间-开始")
    private LocalDateTime redemptionDeadlineStart;

    @Schema(description = "兑换截止时间-结束")
    private LocalDateTime redemptionDeadlineEnd;

    @Schema(description = "退款状态  0-未退款 1-已退款")
    private Integer isRefunded;

    @Schema(description = "是否有效 0-失效 1-有效")
    private Integer isValid;

    @Schema(description = "0-未领取：一个都没领   1-已领取：权益包内商品已领取完毕 2-领取中：权益包内商品未领取完毕  3-领取异常：权益包内商品领取失败")
    private Integer status;

    @Schema(description = "退单时间-开始")
    private LocalDateTime refundedTimeStart;

    @Schema(description = "退单时间-结束")
    private LocalDateTime refundedTimeEnd;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "更新时间-开始")
    private LocalDateTime updateStartTime;

    @Schema(description = "更新时间-结束")
    private LocalDateTime updateEndTime;

    // 以下字段是join表的字段
    @Schema(description = "渠道订单号")
    private String channelOrderNo;

    @Schema(description = "我方生成的订单号")
    private String orderNo;

    @Schema(description = "渠道id")
    private Integer channelId;

    @Schema(description = "渠道名字")
    private String channelName;

    @Schema(description = "支付渠道id")
    private Integer payChannelId;

    @Schema(description = "支付渠道名字")
    private String payChannelName;

    @Schema(description = "支付产品包id")
    private Integer payPkgId;

    @Schema(description = "支付产品包名")
    private String payPkgName;


    public MPJLambdaWrapper<BenefitsPackageOrderDO> buildQueryWrapper(boolean isAsc) {
        BenefitsPackageOrderDO orderDO = MapstructUtils.convertNotNull(this, BenefitsPackageOrderDO.class);
        PageBenefitsPackageStatusEnum statusEnum = PageBenefitsPackageStatusEnum.getByCode(status);
        MPJLambdaWrapper<BenefitsPackageOrderDO> wrapper = new MPJLambdaWrapper<BenefitsPackageOrderDO>()
                .selectAll(BenefitsPackageOrderDO.class)
                .selectAssociation(BenefitOrderExtensionDO.class, BenefitsPackageOrderDO::getOrderExtension)
                .leftJoin(BenefitOrderExtensionDO.class, BenefitOrderExtensionDO::getExtensionId, BenefitsPackageOrderDO::getExtensionId)
                .setEntity(orderDO)
                // 主表模糊查询的几个字段
                .likeRight(mobile != null, BenefitsPackageOrderDO::getMobile, mobile)
                .like(packageName != null, BenefitsPackageOrderDO::getPackageName, packageName)
                .ge(redemptionDeadlineStart != null, BenefitsPackageOrderDO::getRedemptionDeadline, redemptionDeadlineStart)
                .le(redemptionDeadlineEnd != null, BenefitsPackageOrderDO::getRedemptionDeadline, redemptionDeadlineEnd)
                .ge(refundedTimeStart != null, BenefitsPackageOrderDO::getRefundedTime, refundedTimeStart)
                .le(refundedTimeEnd != null, BenefitsPackageOrderDO::getRefundedTime, refundedTimeEnd)
                .ge(createTimeStart != null, BenefitsPackageOrderDO::getCreateTime, createTimeStart)
                .le(createTimeEnd != null, BenefitsPackageOrderDO::getCreateTime, createTimeEnd)
                .ge(updateStartTime != null, BenefitsPackageOrderDO::getUpdateTime, updateStartTime)
                .le(updateEndTime != null, BenefitsPackageOrderDO::getUpdateTime, updateEndTime)
                // join表的查询字段
                .eq(channelOrderNo != null, BenefitOrderExtensionDO::getChannelOrderNo, channelOrderNo)
                .eq(orderNo != null, BenefitOrderExtensionDO::getOrderNo, orderNo)
                .eq(channelId != null, BenefitOrderExtensionDO::getChannelId, channelId)
                .eq(channelName != null, BenefitOrderExtensionDO::getChannelName, channelName)
                .eq(payChannelId != null, BenefitOrderExtensionDO::getPayChannelId, payChannelId)
                .eq(payChannelName != null, BenefitOrderExtensionDO::getPayChannelName, payChannelName)
                .eq(payPkgId != null, BenefitOrderExtensionDO::getPayPkgId, payPkgId)
                .eq(payPkgName != null, BenefitOrderExtensionDO::getPayPkgName, payPkgName)
                .orderBy(true, isAsc, BenefitsPackageOrderDO::getPackageOrderId);
        if (PageBenefitsPackageStatusEnum.UNREDEEMED == statusEnum) {
            wrapper.eq(BenefitsPackageOrderDO::getOptionalDispatchCount, 0)
                    .eq(BenefitsPackageOrderDO::getInstantDispatchCount, 0);
        } else if (PageBenefitsPackageStatusEnum.REDEEMED == statusEnum) {
            String nameMaxSelectable = getDbColName(BenefitsPackageOrderDO::getMaxSelectable);
            String nameOptionalDispatchCount = getDbColName(BenefitsPackageOrderDO::getOptionalDispatchCount);
            String nameInstantCount = getDbColName(BenefitsPackageOrderDO::getInstantCount);
            String nameInstantDispatchCount = getDbColName(BenefitsPackageOrderDO::getInstantDispatchCount);

            wrapper.apply(nameMaxSelectable + "=" + nameOptionalDispatchCount)
                    .apply(nameInstantCount + "=" + nameInstantDispatchCount);
        } else if (PageBenefitsPackageStatusEnum.REDEEMING == statusEnum) {

            String nameMaxSelectable = getDbColName(BenefitsPackageOrderDO::getMaxSelectable);
            String nameOptionalDispatchCount = getDbColName(BenefitsPackageOrderDO::getOptionalDispatchCount);
            String nameInstantCount = getDbColName(BenefitsPackageOrderDO::getInstantCount);
            String nameInstantDispatchCount = getDbColName(BenefitsPackageOrderDO::getInstantDispatchCount);
            wrapper.and(consumer -> consumer
                    .apply(nameMaxSelectable + "!=" + nameOptionalDispatchCount)
                    .or()
                    .apply(nameInstantCount + "!=" + nameInstantDispatchCount)
            );
        } else if (PageBenefitsPackageStatusEnum.REDEEM_ERROR == statusEnum) {
            wrapper.ne(BenefitsPackageOrderDO::getDispatchErrorCount, 0);
        }
        return wrapper;
    }


    public <T> String getDbColName(SFunction<T, ?> fn) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(BenefitsPackageOrderDO.class);
        String name = LambdaUtils.getName(fn);
        for (TableFieldInfo tableFieldInfo : tableInfo.getFieldList()) {
            if (tableFieldInfo.getProperty().equals(name)) {
                return tableFieldInfo.getColumn();
            }
        }
        return null;
    }

}
