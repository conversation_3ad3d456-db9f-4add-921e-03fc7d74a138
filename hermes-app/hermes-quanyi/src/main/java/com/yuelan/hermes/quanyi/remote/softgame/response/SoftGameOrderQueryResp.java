package com.yuelan.hermes.quanyi.remote.softgame.response;

import lombok.Data;

/**
 * 软游通v2订单查询响应
 *
 * <AUTHOR> 2025/7/31
 * @since 2025/7/31
 */
@Data
public class SoftGameOrderQueryResp {

    /**
     * 商户号 - 由SUP系统分配每个商户唯一的一个商户号
     */
    private String businessId;

    /**
     * 商户订单号 - 最大长度不超过32位的唯一流水号
     */
    private String userOrderId;

    /**
     * 订单状态
     * 01 成功（订单最终状态）
     * 02 失败（订单最终状态）
     * 03 处理中（需要等待异步通知结果）
     * 04 订单不存在
     * 05 未知错误
     * 06 签名错误
     * 07 参数有误
     */
    private String status;

    /**
     * 结果说明 - 对充值的结果予以说明，特别是充值失败的时候需要对具体充值失败原因简单说明一下
     */
    private String mes;

    /**
     * 结算总金额 - 系统和进货平台结算金额
     */
    private String payoffPriceTotal;

    /**
     * 卡密信息 - kmInfo 为 3DES 加密过的字符串
     */
    private String kmInfo;

    /**
     * 签名 - md5 (businessId + userOrderId + status +密钥)
     */
    private String sign;

    /**
     * 充值凭证 - 话费类型商品的充值凭证
     */
    private String voucherNo;

    /**
     * 判断是否为最终状态
     */
    public boolean isFinalStatus() {
        return "01".equals(status) || "02".equals(status);
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return "01".equals(status);
    }

    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return "02".equals(status);
    }

    /**
     * 判断是否处理中
     */
    public boolean isProcessing() {
        return "03".equals(status);
    }
}
