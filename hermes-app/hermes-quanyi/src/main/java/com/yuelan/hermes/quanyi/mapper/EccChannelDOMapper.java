package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccChannelDO;

/**
 * <AUTHOR> 2024/5/4 上午1:07
 */
public interface EccChannelDOMapper extends BaseMapper<EccChannelDO> {
    default EccChannelDO selectByChannelName(String channelName) {
        return selectOne(Wrappers.lambdaQuery(EccChannelDO.class)
                .eq(EccChannelDO::getChannelName, channelName));
    }
}