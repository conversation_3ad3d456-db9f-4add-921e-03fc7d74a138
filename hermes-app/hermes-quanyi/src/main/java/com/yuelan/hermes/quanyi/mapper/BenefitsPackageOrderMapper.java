package com.yuelan.hermes.quanyi.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageOrderDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2025/4/21
 * @since 2025/4/21
 */
public interface BenefitsPackageOrderMapper extends MPJBaseMapper<BenefitsPackageOrderDO> {

    /**
     * 更新权益包订单的权益发放信息
     *
     * @param packageOrderId                 权益包订单id
     * @param dispatchErrorCountIncrement    发放失败次数增量
     * @param instantDispatchCountIncrement  即时发放次数增量
     * @param optionalDispatchCountIncrement 可选发放次数增量
     * @return 更新结果
     */
    int updateDispatchInfoById(@Param("packageOrderId") Long packageOrderId,
                               @Param("dispatchErrorCountIncrement") int dispatchErrorCountIncrement,
                               @Param("instantDispatchCountIncrement") int instantDispatchCountIncrement,
                               @Param("optionalDispatchCountIncrement") int optionalDispatchCountIncrement);

    int updateRefundStatusByIds(@Param("ids") List<Long> ids);
}