package com.yuelan.hermes.quanyi.remote.jinxi.response;

import lombok.Data;

/**
 * 今溪订单充值响应
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Data
public class JinXiOrderRechargeResp {

    /**
     * 是否是实时到账
     * 业务的逻辑值
     */
    public boolean realTime;
    /**
     * 购买数量
     */
    private Integer buyNumber;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 客户订单号，每次请求必须唯一
     */
    private String customOrderId;
    /**
     * 订单编号
     */
    private String orderId;
    /**
     * 订单类型，10. 直冲订单 20. 卡密订单 30. 话费订单 40. 流量订单 50.号卡订单
     */
    private Integer orderType;
    /**
     * 商品编号
     */
    private String productId;
    /**
     * 商品名称
     */
    private String productName;

    public boolean isRealTimeSuccess() {
        return realTime;
    }
}
