package com.yuelan.hermes.quanyi.remote.hbsk.request;

import lombok.Data;

/**
 * 爱音乐订购退订消息回调请求
 *
 * <AUTHOR> 2025/7/17
 * @since 2025/7/17
 */
@Data
public class ImusicCallbackReq {

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * ISMP产品ID
     */
    private String productid;

    /**
     * 状态 0-订购，1-退订,2-延迟计费生效（未正式计费）,3-延迟计费退订
     */
    private String state;

    /**
     * 操作时间 格式 yyyy-MM-dd HH:mm:ss
     */
    private String time;

    /**
     * 订单唯一标识（可选）
     */
    private String orderNo;

    /**
     * 用户设备ID（可选）
     */
    private String deviceNo;

    /**
     * 订单流水号（可选）
     */
    private String streamingNo;

    /**
     * 权益生效时间（可选）格式 yyyy-MM-dd HH:mm:ss
     */
    private String orderTime;

    /**
     * 权益失效时间（可选）格式 yyyy-MM-dd HH:mm:ss
     */
    private String validTime;

    /**
     * 外部订单号（可选）
     */
    private String extOrderNo;

    // 请求头中的字段
    /**
     * 设备ID（请求头）
     */
    private String deviceid;

    /**
     * 时间戳（请求头）格式：yyyyMMddHHmmss
     */
    private String timestamp;

    /**
     * 签名（请求头）
     */
    private String signatureHmacsha256;
}
