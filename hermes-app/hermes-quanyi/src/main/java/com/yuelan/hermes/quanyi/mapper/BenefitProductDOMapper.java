package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;

import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 16:17
 */
public interface BenefitProductDOMapper extends BaseMapper<BenefitProductDO> {


    default Integer selectCountByProdCode(String prodCode) {
        return selectCount(Wrappers.<BenefitProductDO>lambdaQuery().eq(BenefitProductDO::getProdCode, prodCode)).intValue();
    }

    default BenefitProductDO selectByProdCodeAndProdStatus(String prodCode, Integer prodStatus) {
        return selectOne(Wrappers.<BenefitProductDO>lambdaQuery()
                .eq(BenefitProductDO::getProdCode, prodCode)
                .eq(Objects.nonNull(prodStatus), BenefitProductDO::getProdStatus, prodStatus)
        );
    }
}