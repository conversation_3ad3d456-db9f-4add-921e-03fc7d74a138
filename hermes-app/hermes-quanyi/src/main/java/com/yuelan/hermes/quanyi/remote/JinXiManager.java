package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.common.pojo.properties.JinXiProperties;
import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiBaseReq;
import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiOrderQueryReq;
import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiOrderRechargeReq;
import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiProductAuthReq;
import com.yuelan.hermes.quanyi.remote.jinxi.response.JinXiBaseResponse;
import com.yuelan.hermes.quanyi.remote.jinxi.response.JinXiOrderQueryResp;
import com.yuelan.hermes.quanyi.remote.jinxi.response.JinXiOrderRechargeResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 今溪管理器
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Service
@Slf4j
@AllArgsConstructor
public class JinXiManager {

    private final JinXiProperties jinXiProperties;

    /**
     * 生成订单充值签名
     * 签名规则：
     * 1. 将除sign参数之外的所有参数包装成Dictionary
     * 2. 将Dictionary按照key进行升序排列
     * 3. 把所有参数名和参数值串在一起
     * 4. 在字符串前后拼接应用秘钥
     * 5. 使用MD5进行加密（32位大写）
     */
    public static String generateOrderRechargeSign(Object requestObj, String signKey) {
        Map<String, Object> params = JSONObject.from(requestObj);

        // 第二步：将Dictionary按照key进行升序排列
        String sortedParams = params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + entry.getValue())
                .reduce("", String::concat);

        // 第三步和第四步：把所有参数名和参数值串在一起，在字符串前后拼接应用秘钥
        String signData = signKey + sortedParams + signKey;

        log.info("今溪签名原串: {}", signData);
        // 第五步：使用MD5进行加密（32位大写）
        return SecureUtil.md5(signData).toUpperCase();
    }

    /**
     * 订单查询
     */
    public JinXiBaseResponse<JinXiOrderQueryResp> queryOrder(JinXiOrderQueryReq req) {
        prepareBaseRequest(req);
        String url = jinXiProperties.getOrderQueryUrl();
        log.info("今溪订单查询请求地址: {}", url);
        log.info("今溪订单查询请求参数: {}", JSON.toJSONString(req));
        try (HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "multipart/form-data")
                .form(JSONObject.from(req))
                .execute()) {
            String body = response.body();
            log.info("今溪订单查询返回参数: {}", body);
            return JSON.parseObject(body, new TypeReference<JinXiBaseResponse<JinXiOrderQueryResp>>() {
            });
        }
    }

    public static void main(String[] args) {
        JinXiProperties jinXiProperties = new JinXiProperties();
        JinXiManager jinXiManager = new JinXiManager(jinXiProperties);
        JinXiOrderQueryReq jinXiOrderQueryReq = new JinXiOrderQueryReq();
        jinXiOrderQueryReq.setCustomOrderId("BI25081953752636030689280");
        jinXiManager.queryOrder(jinXiOrderQueryReq);
    }

    /**
     * 订购商品鉴权接口
     */
    private JinXiBaseResponse<Boolean> phoneAndProductAuth(JinXiProductAuthReq req, StringBuilder reqBuild, StringBuilder respBuild) {
        // 设置基础参数和签名
        prepareBaseRequest(req);
        reqBuild.append(JSON.toJSONString(req));

        String url = jinXiProperties.getProductAuthUrl();
        log.info("今溪订购商品鉴权请求地址: {}", url);
        log.info("今溪订购商品鉴权请求参数: {}", JSON.toJSONString(req));
        try (HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "multipart/form-data")
                .form(JSONObject.from(req))
                .execute()) {
            String body = response.body();
            respBuild.append(body);
            log.info("今溪订购商品鉴权返回参数: {}", body);
            return JSON.parseObject(body, new TypeReference<JinXiBaseResponse<Boolean>>() {
            });
        }
    }

    /**
     * 订单充值（异步通知-非实时到账）
     *
     * @param req       订单充值请求
     * @param reqBuild  请求日志构建器
     * @param respBuild 响应日志构建器
     * @return 统一响应格式
     */
    public JinXiBaseResponse<JinXiOrderRechargeResp> orderRecharge(JinXiOrderRechargeReq req,
                                                                   StringBuilder reqBuild,
                                                                   StringBuilder respBuild) {

        if (!AppConstants.isReal()) {
            JinXiBaseResponse<JinXiOrderRechargeResp> orderResp = new JinXiBaseResponse<>();
            orderResp.setCode("200");
            orderResp.setMessage("模拟成功");
            JinXiOrderRechargeResp data = new JinXiOrderRechargeResp();
            data.setOrderId("TEST_ORDER_" + System.currentTimeMillis());
            data.setRealTime(Boolean.TRUE);
            orderResp.setData(data);
            return orderResp;
        }

        // 设置基础参数和签名
        prepareBaseRequest(req);

        reqBuild.append(JSON.toJSONString(req));

        String url = jinXiProperties.getOrderRechargeUrl();
        log.info("今溪订单充值请求地址: {}", url);
        log.info("今溪订单充值请求参数: {}", JSON.toJSONString(req));

        try (HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "multipart/form-data")
                .form(JSONObject.from(req))
                .execute()) {

            String body = response.body();
            respBuild.append(body);
            log.info("今溪订单充值返回参数: {}", body);
            JinXiBaseResponse<JinXiOrderRechargeResp> orderResp = JSON.parseObject(body, new TypeReference<JinXiBaseResponse<JinXiOrderRechargeResp>>() {
            });
            // 对接的是异步通知接口所以这里是不会实时到账的
            if (orderResp.isSuccess() && orderResp.getData() != null) {
                orderResp.getData().setRealTime(Boolean.FALSE);
            }
            return orderResp;
        }
    }

    /**
     * 设置基础参数和签名
     *
     * @param req 基础请求对象
     */
    private void prepareBaseRequest(JinXiBaseReq req) {
        // 设置基础参数
        req.setApp_key(jinXiProperties.getAppKey());
        req.setVersion(jinXiProperties.getVersion());
        req.setSign_type(jinXiProperties.getSignType());
        req.setTimestamp(LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_PATTERN));

        // 生成签名（在设置sign之前）
        String sign = generateOrderRechargeSign(req, jinXiProperties.getSignKey());
        req.setSign(sign);
    }


}
