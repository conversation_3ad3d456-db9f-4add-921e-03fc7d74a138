package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.enums.WoReadWxPayV2PkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.properties.WoReadWxPayV2Properties;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> 2025/6/23
 * @since 2025/6/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WoReadWxPayV2Manager {
    private static final String ORIGIN_VAL = "https://m.woread.com.cn";
    private static final String ACCEPT_VAL = "application/json, text/plain, */*";
    private static final String USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/605.1 NAVER(inapp; search; 2000; 12.14.1; 16PROMAX)";

    private final WoReadWxPayV2Properties woReadWxPayV2Properties;

    /**
     * 随机的 uuid
     */
    public static String getUuidManually() {
        String template = "xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx";
        StringBuilder uuidBuilder = new StringBuilder();

        for (char c : template.toCharArray()) {
            if (c == 'x' || c == 'y') {
                // 生成一个 0-15 的随机整数
                int r = RandomUtil.getRandom().nextInt(16);
                // 根据占位符决定最终值
                int v = (c == 'x') ? r : (r & 0x3 | 0x8);
                // 将整数转换为十六进制字符并追加
                uuidBuilder.append(Integer.toHexString(v));
            } else {
                // 如果不是占位符（比如'4'），直接追加
                uuidBuilder.append(c);
            }
        }
        return uuidBuilder.toString();
    }

    private WoReadWxPayV2Properties.ProdConfig getProdConfig(WoReadWxPayV2PkgEnum payPkgEnum) {
        return woReadWxPayV2Properties.getProdConfigMap().get(payPkgEnum);
    }

    public PrepayResp getWxPayUrl(String mobile, WoReadWxPayV2PkgEnum payPkgEnum) {
        WoReadWxPayV2Properties.ProdConfig prodConfig = getProdConfig(payPkgEnum);

        ThirdPartyActivityResp thirdPartyUrlResp = getThirdPartyUrl(prodConfig);
        ThirdPartyActivityResp.MessageData messageData = thirdPartyUrlResp.getMessage();
        String pageUrl = messageData.getPageUrl();
        String channelId = messageData.getChannelId();
        // https://m.woread.com.cn/activity_h5/monthMarket?id=cfbdb09fea714337b55ced5bdfe5a8de&channelid=15796641
        // https://m.woread.com.cn/activity_h5/monthMarket?id=cfbdb09fea714337b55ced5bdfe5a8de&channelid=15796641&wokey=611aec1d9f4e35945b16a7babfd0c349&pageid=f2b4228f14d74b37b752c35494316afa
        pageUrl = pageUrl + "&wokey=" + messageData.getKey() + "&pageid=" + prodConfig.getPageId();

        String activeIndex = null;
        Pattern pattern = Pattern.compile("id=([^&]*)");
        Matcher matcher = pattern.matcher(pageUrl);
        if (matcher.find()) {
            activeIndex = matcher.group(1);
        }
        log.info("解析到的活动索引: {} 长度{}", activeIndex, activeIndex.length());
        // 1. 获取 accessToken 之后的请求里面需要
        AccessTokenResp accessTokenResp = getAccessToken(pageUrl, prodConfig);
        String accessToken = accessTokenResp.getData().getAccesstoken();
        String aesKey = accessToken.substring(16);

        // 2 wsShareShal 只是请求返回的数据用不上
        LocalDateTime now = LocalDateTime.now();
        String timestamp = LocalDateTimeUtil.format(now, DatePattern.PURE_DATETIME_PATTERN);
        long signTimestamp = now.toInstant(ZoneOffset.UTC).toEpochMilli();
        wsShareShal(prodConfig, pageUrl, accessToken, aesKey, timestamp, signTimestamp);

        // 3. queryPkgActive 获取包的信息
        JSONObject pkgActiveJson = queryPkgActive(prodConfig, pageUrl, activeIndex, accessToken, aesKey, timestamp, signTimestamp);
        String productId = pkgActiveJson.getJSONObject("data").getString("productpkgindex");

        // 4. marketingOrderReport
        // marketingOrderReport  要新时间戳
        String newTimestamp = System.currentTimeMillis() + "";
        marketingOrderReport(prodConfig, pageUrl, productId, newTimestamp);

        // 5.核心获取支付链接
        return thirdPartyActivity(prodConfig, pageUrl, activeIndex, channelId, mobile, accessToken, aesKey, timestamp, signTimestamp);
    }


    public ThirdPartyActivityResp getThirdPartyUrl(WoReadWxPayV2Properties.ProdConfig prodConfig) {
        // curl 'https://m.woread.com.cn/touchactivity/marketingControlMiddle/getThirdPartyUrl.action' \
        //   -H 'Accept: application/json, text/javascript, */*; q=0.01' \
        //   -H 'Accept-Language: zh-CN,zh;q=0.9' \
        //   -H 'Cache-Control: no-cache' \
        //   -H 'Connection: keep-alive' \
        //   -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8' \
        //   -b 'JSESSIONID=0CCED7E522F390E4338B8F3400F199B5; route=93a02d4ac018963c88e15fe1af7c0ed2' \
        //   -H 'Origin: https://m.woread.com.cn' \
        //   -H 'Pragma: no-cache' \
        //   -H 'Referer: https://m.woread.com.cn/touchactivity/marketingControlMiddle/index.action?pageid=f2b4228f14d74b37b752c35494316afa' \
        //   -H 'Sec-Fetch-Dest: empty' \
        //   -H 'Sec-Fetch-Mode: cors' \
        //   -H 'Sec-Fetch-Site: same-origin' \
        //   -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
        //   -H 'X-Requested-With: XMLHttpRequest' \
        //   -H 'sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"' \
        //   -H 'sec-ch-ua-mobile: ?0' \
        //   -H 'sec-ch-ua-platform: "macOS"' \
        //   --data-raw 'platformid=0&pageid=f2b4228f14d74b37b752c35494316afa&appagent=Mozilla%2F5.0+(Macintosh%3B+Intel+Mac+OS+X+10_15_7)+AppleWebKit%2F537.36+(KHTML%2C+like+Gecko)+Chrome%2F*********+Safari%2F537.36&refUrl=https%3A%2F%2Fm.woread.com.cn%2Ftouchactivity%2FmarketingControlMiddle%2Findex.action%3Fpageid%3Df2b4228f14d74b37b752c35494316afa&releaseip=&releasetype=&browserinfo=&userip=&domainname=&channelname=&accesstime='

        String url = "https://m.woread.com.cn/touchactivity/marketingControlMiddle/getThirdPartyUrl.action";
        String refUrl = "https://m.woread.com.cn/touchactivity/marketingControlMiddle/index.action?pageid=" + prodConfig.getPageId();

        Map<String, Object> params = new HashMap<>();
        params.put("platformid", "0");
        params.put("pageid", prodConfig.getPageId());
        params.put("appagent", USER_AGENT);
        params.put("refUrl", "https://m.woread.com.cn/touchactivity/marketingControlMiddle/index.action?pageid=" + prodConfig.getPageId());
        params.put("releaseip", "");
        params.put("releasetype", "");
        params.put("browserinfo", "");
        params.put("userip", "");
        params.put("domainname", "");
        params.put("channelname", "");

        HttpRequest post = HttpRequest.post(url).form(params);
        post.header(Header.ACCEPT.getValue(), "application/json, text/javascript, */*; q=0.01");
        post.header(Header.ACCEPT_LANGUAGE.getValue(), "zh-CN,zh;q=0.9");
        post.header(Header.CONTENT_TYPE.getValue(), "application/x-www-form-urlencoded; charset=UTF-8");
        post.header(Header.ORIGIN.getValue(), ORIGIN_VAL);
        post.header(Header.REFERER.getValue(), refUrl);
        post.header(Header.USER_AGENT.getValue(), USER_AGENT);
        post.header("X-Requested-With", "XMLHttpRequest");


        HttpResponse execute = post.execute();
        String response = execute.body();
        log.info("getThirdPartyUrl 响应: {}", response);

        return JSON.parseObject(response, ThirdPartyActivityResp.class);
    }

    /**
     * 获取 accessToken 之后的请求 head 里面需要
     */
    private AccessTokenResp getAccessToken(String pageUrl, WoReadWxPayV2Properties.ProdConfig prodConfig) {

        // curl 'https://m.woread.com.cn/api/union/app/auth/10000006/1750677077745/22744a80ba5373a598047bf56d2314d7' \
        // -H 'Accept: application/json, text/plain, */*' \
        // -H 'Accept-Language: zh-CN,zh;q=0.9' \
        // -H 'Cache-Control: no-cache' \
        // -H 'Connection: keep-alive' \
        // -H 'Content-Type: application/json' \
        // -H 'Pragma: no-cache' \
        // -H 'Referer: https://m.woread.com.cn/activity_h5/monthMarket?id=cfbdb09fea714337b55ced5bdfe5a8de&wokey=a26e7a1efa5a00e1c9cdae1b07e6ed9d&pageid=f2b4228f14d74b37b752c35494316afa' \
        // -H 'Sec-Fetch-Dest: empty' \
        // -H 'Sec-Fetch-Mode: cors' \
        // -H 'Sec-Fetch-Site: same-origin' \
        // -H 'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'

        long timestamp = System.currentTimeMillis();
        String uri = SecureUtil.md5(prodConfig.getAppId() + prodConfig.getKey() + timestamp);
        String url = String.format("https://m.woread.com.cn/api/union/app/auth/%s/%d/%s", prodConfig.getAppId(), timestamp, uri);

        HttpRequest get = HttpRequest.get(url);
        get.header(Header.ACCEPT.getValue(), ACCEPT_VAL);
        get.header(Header.ACCEPT_LANGUAGE.getValue(), "zh-CN,zh;q=0.9");
        get.header(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
        get.header(Header.REFERER.getValue(), pageUrl);
        get.header(Header.USER_AGENT.getValue(), USER_AGENT);
        // 发送请求并获取响应
        String response = get.execute().body();
        log.info("getAccessToken 响应: {}", response);
        return JSON.parseObject(response, AccessTokenResp.class);
    }

    private void wsShareShal(WoReadWxPayV2Properties.ProdConfig prodConfig, String pageUrl, String accessToken, String aesKey, String timestamp, long signTimestamp) {
        final String url = "https://m.woread.com.cn/api/wolisten/user/wsShareShal";
        HttpRequest post = HttpRequest.post(url);
        post.header(Header.ACCEPT.getValue(), ACCEPT_VAL);
        post.header(Header.ACCEPT_LANGUAGE.getValue(), "zh-CN,zh;q=0.9,en;q=0.8");
        post.header("accesstoken", accessToken);
        post.header(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
        post.header(Header.ORIGIN.getValue(), ORIGIN_VAL);
        post.header(Header.REFERER.getValue(), pageUrl);
        post.header(Header.USER_AGENT.getValue(), USER_AGENT);
        JSONObject sourceBody = new JSONObject();
        sourceBody.put("shareUrl", pageUrl);
        sourceBody.put("source", "9");
        sourceBody.put("timestamp", timestamp);
        sourceBody.put("signtimestamp", signTimestamp);

        String sign = encodeAes2(sourceBody.toJSONString(), aesKey, prodConfig.getIvStr());
        JSONObject postBody = new JSONObject();
        postBody.put("sign", sign);
        post.body(postBody.toJSONString());
        log.info("wsShareShal请求{}", post);
        // 发送请求并获取响应
        String response = post.execute().body();
        log.info("wsShareShal 响应: {}", response);
    }

    private JSONObject queryPkgActive(WoReadWxPayV2Properties.ProdConfig prodConfig, String pageUrl, String activeIndex, String accessToken, String aesKey, String timestamp, long signTimestamp) {
        // curl 'https://m.woread.com.cn/api/union/activity/queryPkgActive' \
        //   -H 'accept: application/json, text/plain, */*' \
        //   -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8' \
        //   -H 'accesstoken: NTI4RDdGMUU0NUM2QzUzOUM2QTQ2MTc4' \
        //   -H 'cache-control: no-cache' \
        //   -H 'content-type: application/json' \
        //   -b 'ireaddata2018jssdkcross=%7B%22distinct_id%22%3A%221978c149be81691-03093999e7560e8-18525636-5089536-1978c149be91ca0%22%2C%22%24device_id%22%3A%221978c149be81691-03093999e7560e8-18525636-5089536-1978c149be91ca0%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%7D' \
        //   -H 'origin: https://m.woread.com.cn' \
        //   -H 'pragma: no-cache' \
        //   -H 'priority: u=1, i' \
        //   -H 'random: 408014' \
        //   -H 'referer: https://m.woread.com.cn/activity_h5/monthMarket?id=cfbdb09fea714337b55ced5bdfe5a8de&wokey=a26e7a1efa5a00e1c9cdae1b07e6ed9d&pageid=f2b4228f14d74b37b752c35494316afa' \
        //   -H 'sec-fetch-dest: empty' \
        //   -H 'sec-fetch-mode: cors' \
        //   -H 'sec-fetch-site: same-origin' \
        //   -H 'sign: MjM5NzVhMWY5OTBjYzI0YTJjYTFjZmE0OTViYjEzYjdhZjQ4NDNhMjAzNDZiZDkyY2MwZDE4MmQwMWZiZTNhZjc3MmIyYzE5ZTk2NWE5NDkxMDUwM2M3MjY4OGZkNGY2NmYzNmUzN2RhOGY1MTRlZmNlMWQ1NGNmZGEzNWUwMzIxNWViZmI0Nzc3MzBmNzNmYTI3MDM3OGQ2MzQ5YzAwM2VmYTFlNjk1NTIzMDA2ZTk2N2E5YjVlOWIzYTAzNTVjMDRiMjllYjI1MDBmMmU5NDAwYjhmYWJlMWQ2MTJjMDU1OTZjODFhMTlmODBiMjM4ZDA1NGEzZDMxNTIxMWI4YQ==' \
        //   -H 'source: 9' \
        //   -H 'timestamp: 1750727023972' \
        //   -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1' \
        //   --data-raw '{"sign":"MjM5NzVhMWY5OTBjYzI0YTJjYTFjZmE0OTViYjEzYjdhZjQ4NDNhMjAzNDZiZDkyY2MwZDE4MmQwMWZiZTNhZjc3MmIyYzE5ZTk2NWE5NDkxMDUwM2M3MjY4OGZkNGY2NmYzNmUzN2RhOGY1MTRlZmNlMWQ1NGNmZGEzNWUwMzIxNWViZmI0Nzc3MzBmNzNmYTI3MDM3OGQ2MzQ5YzAwM2VmYTFlNjk1NTIzMDA2ZTk2N2E5YjVlOWIzYTAzNTVjMDRiMjllYjI1MDBmMmU5NDAwYjhmYWJlMWQ2MTJjMDU1OTZjODFhMTlmODBiMjM4ZDA1NGEzZDMxNTIxMWI4YQ=="}'
        // 请求参数 {"activeindex":"cfbdb09fea714337b55ced5bdfe5a8de","source":"9","timestamp":"20250624090343","signtimestamp":1750727023972}
        JSONObject sourceBody = new JSONObject();
        sourceBody.put("source", "9");
        sourceBody.put("activeindex", activeIndex);
        sourceBody.put("timestamp", timestamp);
        sourceBody.put("signtimestamp", signTimestamp);

        String sign = encodeAes2(sourceBody.toJSONString(), aesKey, prodConfig.getIvStr());
        JSONObject postBody = new JSONObject();
        postBody.put("sign", sign);


        final String url = "https://m.woread.com.cn/api/union/activity/queryPkgActive";
        HttpRequest post = HttpRequest.post(url);
        post.header(Header.ACCEPT.getValue(), ACCEPT_VAL);
        post.header(Header.ACCEPT_LANGUAGE.getValue(), "zh-CN,zh;q=0.9,en;q=0.8");
        post.header("accesstoken", accessToken);
        post.header(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
        post.header(Header.ORIGIN.getValue(), ORIGIN_VAL);
        post.header("random", String.valueOf((int) Math.floor(1e6 * Math.random())));
        post.header(Header.REFERER.getValue(), pageUrl);
        post.header("sign", sign);
        post.header("source", "9");
        post.header("timestamp", timestamp);
        post.header("user-agent", USER_AGENT);
        post.body(postBody.toJSONString());
        log.info("queryPkgActive请求: {}", post);
        // 发送请求并获取响应
        String response = post.execute().body();
        log.info("queryPkgActive 响应: {}", response);
        return JSON.parseObject(response);
    }

    private void marketingOrderReport(WoReadWxPayV2Properties.ProdConfig prodConfig, String pageUrl, String productId, String timestamp) {
        // curl 'https://m.woread.com.cn/api/union/common/marketingOrderReport' \
        //   -H 'Accept: */*' \
        //   -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
        //   -H 'Cache-Control: no-cache' \
        //   -H 'Connection: keep-alive' \
        //   -H 'Content-type: application/json' \
        //   -b 'ireaddata2018jssdkcross=%7B%22distinct_id%22%3A%221978c149be81691-03093999e7560e8-18525636-5089536-1978c149be91ca0%22%2C%22%24device_id%22%3A%221978c149be81691-03093999e7560e8-18525636-5089536-1978c149be91ca0%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%7D' \
        //   -H 'Origin: https://m.woread.com.cn' \
        //   -H 'Pragma: no-cache' \
        //   -H 'Referer: https://m.woread.com.cn/activity_h5/monthMarket?id=cfbdb09fea714337b55ced5bdfe5a8de&wokey=a26e7a1efa5a00e1c9cdae1b07e6ed9d&pageid=f2b4228f14d74b37b752c35494316afa' \
        //   -H 'Sec-Fetch-Dest: empty' \
        //   -H 'Sec-Fetch-Mode: cors' \
        //   -H 'Sec-Fetch-Site: same-origin' \
        //   -H 'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1' \
        //   -H 'api-source: 1' \
        //   --data-raw '{"sign":"ZTAzYTUyZTY4MzhkNWFiNzBjNGMyZTUxODIyYjQ0NDgzMjMxMmE4Nzc1MDM0NDA4Y2UzMzIwYzY1NDIzMGI0YzFmYmY2YzgxMTU0YTc1N2EzYzRlNmNlODhhMmMxODIzOWUyMzA1ZmQ0OTlkZmY0ODVkZGRhNjI1OGJlMjBlOTVhMTVlY2VlNGFkOTU4M2U4NjJjMWVkYzcwZTk4ZjI4NjI0OWVkMjY3ZDE5NTlmYTE0NmE3OTUxYmJkMWE4YzEyNGU0OWRiODdmNDMxMTYyNDMxNjg0NmQ1ODk0OTJjNzYzZWMwZWNjZTI3YzA2NjM1OGFlMDZhMmQwZDJiNTNlOTg3OWVmYzI1YjAzMTk1ZjNkYzFiOGVhM2NmMGU5ZmQ4MjU4YjE5MjViNjZmZmQ0YjUzOTdkMTk5MzRmYWUzZjIzNWIyNDg3YjZkMzA2YThmZGRlNWQ4ZGNjN2NjMDYzMWZkNTE5Yzc1ZjQ1OTIwZGRiNTY4MTJiMTEzNGI5N2NhZGNiYWIyZjlhNjNkYTQ2NmNjM2VlYTE5OTE5YzdkYmI2YzNlOWM1YjIxYTJmMmIzOGI5MGI1MjM2NWFkZjIyMDRlNzIwMGNmYTVkOWYyMTIwZDhkNzc0MWNhNzA0NjJhODkxM2Y4ZTAwZDAyMzYzZWM0MjAyZjE3OGYzYjE4NmZkYWNhMDEwNGI4ODRjYTU2YTdiMGM2ZmFiM2ZiNzQ3YWZhZmYxM2E5MjhhNjg1NGEyMzVhYWY5MTMyNzQyZGEzM2ViM2Q3M2RlNWU4Yzc2YzYxZDJhMTE4NTE2YzJkODcxZTJlYTU3ODM1OTA5ZWE0OTI0MjNkMGI4OTQ0ZTM4NzE3Y2IwZWY3ODIwZDUxY2RiNDY0ODA3ZmI3NjRlOTA0MjAwZWM2YjYzNzU5NjU1NGE4NGFhZmU2Y2QxMWNhMGRmZmUyM2E3YjJjYWRhNTA1MDVhYWExNWM0NTYxYTgwOTAzNmU0NTJjMmMwZDRhNDRmNTY1ZDIxNDUwOTZmZmYxMjRiYmZjM2ZjZjIzNWNkYTU1ZDQ1ZDcyYTcxNmE4ODlhZjdkODU1NmQyMTk0ZWNjNmFjY2QzNmRlYzZiNzdiZDYyNDMwMTE1M2VkMTdlMDdiMjE3NjczMDQwNTAwM2IyZWQ1YmE3NDVmMjE1Y2I3MmI4NTliYzA0YmM4YzliM2Q3OWYxNmM1NjRmMjcwZmVlNDQ1NTMzNzQ5OTE3MTllMzEwMTdhN2E1ZmIxYTUzZDdjZGJkNDZhMjUwYmFkYTE0"}'

        //{
        //     "deptId": "7",
        //     "eventType": 1,
        //     "orderUrl": "wolisten",
        //     "pageUrl": "https://m.woread.com.cn/activity_h5/monthMarket?id=cfbdb09fea714337b55ced5bdfe5a8de&wokey=a26e7a1efa5a00e1c9cdae1b07e6ed9d&pageid=f2b4228f14d74b37b752c35494316afa",
        //     "productid": 11084,
        //     "timestamp": 1750729952198,
        //     "treesid": "100033f131484d5bb211e0424f1fa46a",
        //     "type": 1,
        //     "userUa": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
        // }
        JSONObject sourceBody = new JSONObject();
        sourceBody.put("deptId", "7");
        sourceBody.put("eventType", 1);
        sourceBody.put("orderUrl", "wolisten");
        sourceBody.put("pageUrl", pageUrl);
        sourceBody.put("productid", productId);
        sourceBody.put("timestamp", timestamp);
        sourceBody.put("treesid", getUuidManually());
        sourceBody.put("type", 1);
        sourceBody.put("userUa", USER_AGENT);

        String sign = encodeAes2(sourceBody.toJSONString(), prodConfig.getAesKey2(), prodConfig.getIvStr());
        JSONObject postBody = new JSONObject();
        postBody.put("sign", sign);

        final String url = "https://m.woread.com.cn/api/union/common/marketingOrderReport";
        HttpRequest post = HttpRequest.post(url);
        post.header(Header.ACCEPT.getValue(), "*/*");
        post.header(Header.ACCEPT_LANGUAGE.getValue(), "zh-CN,zh;q=0.9,en;q=0.8");
        post.header(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());
        post.header(Header.ORIGIN.getValue(), ORIGIN_VAL);
        post.header(Header.REFERER.getValue(), pageUrl);
        post.header(Header.USER_AGENT, USER_AGENT);
        post.header("api-source", "1");
        post.body(postBody.toJSONString());
        log.info("marketingOrderReport请求: {}", post);
        // 发送请求并获取响应
        String response = post.execute().body();
        log.info("marketingOrderReport 响应: {}", response);

    }

    private PrepayResp thirdPartyActivity(WoReadWxPayV2Properties.ProdConfig prodConfig, String pageUrl, String activeIndex, String channelId, String phone, String accessToken, String aesKey, String timestamp, long signTimestamp) {
        // curl 'https://m.woread.com.cn/api/union/pay/wechat/thirdPartyActivity' \
        // -X POST \
        // -H 'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1' \
        // -H 'Accept: application/json, text/plain, */*' \
        // -H 'accesstoken: NTI4RDdGMUU0NUM2QzUzOUM2QTQ2MTc4' \
        // -H 'Origin: https://m.woread.com.cn' \
        // -H 'Sec-Fetch-Site: same-origin' \
        // -H 'Sec-Fetch-Mode: cors' \
        // -H 'Sec-Fetch-Dest: empty' \
        // -H 'Referer: https://m.woread.com.cn/activity_h5/monthMarket?id=cfbdb09fea714337b55ced5bdfe5a8de&wokey=a26e7a1efa5a00e1c9cdae1b07e6ed9d&pageid=f2b4228f14d74b37b752c35494316afa' \
        // -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8' \
        // -H 'Content-Type: application/json' \
        // --cookie 'ireaddata2018jssdkcross=%7B%22distinct_id%22%3A%221978c149be81691-03093999e7560e8-18525636-5089536-1978c149be91ca0%22%2C%22%24device_id%22%3A%221978c149be81691-03093999e7560e8-18525636-5089536-1978c149be91ca0%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_referrer%22%3A%22%22%2C%22%24latest_referrer_host%22%3A%22%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%7D%7D' \
        // --data-raw '{"sign":"ZDRlNDZmNjRmZmEwMmEzZDhmNjkzODQ3Y2YyNDkwNThkYjQyZDkwM2Q4YjMxODllODk5NTgwMzMzZTQ1ZTU4ZTM4NDQ2OTBkYzVjZjdkYjBhNDdkNmFiNDEyZWYwODY2ODgyNTYxOWRkNjNhYmUxOTU2NzM0MmUxOTFkY2U0ZjhkNmU3MmQzOWFjYTI0ZmEwYzJlMWIxNWEwNGJjMzA1ODFhNTYzODAwYzhhYTgyNWE2NWIwMmFjMGM3N2YzMjgyZWZlMDk1ZGY2ZWVkZDkwMDhmMTU0YWFlNzEyMmY3MzBiNWY1ZGU0OGYxODViMTE1ZWRkYTQyZWIyNGNkNGM1ZWZmZjAyNDdhZjdhMTNmYTcyNTI0MDc5MDU5MmNlMmY3NjA1YTZmYWY1MTU3Y2M5OTRmZmRiMzg1NmIyNDRlOGJhNjg2M2FlYTg2ZmZmNzBiNzdmN2Q1M2Y2ZjBmN2Q1YzdlYmY3YWJkNWNlN2MxMGFjMTQ4YzhjNGZhMDBlNGEzZWVhNmVhN2FjNTFmNzhhZmI1YTFkN2JkNjQ1Yzk5MmY5YzE5ZTYyODliNmVhMDY0NTg0ZjEzNGY4ZGU0NzlmNzc3NGY3MzZlOTEyNWQ4NDIzOGIxMjFkODUyMTNmNDM5OWE0NWNjOTBiYWU2ZGQ1NzMzODE4MmViMDBmNjkwYzFjMGU4Y2VkMTIxMDhjZjA2Mjk5ODIzMmExYWNjMWYxYmUxY2RkZDQ3M2U5MjMzZWYzMTM0NmFiZTU3NTg4ZWRiNjM4MGNjZmQzOTUyNjEzNmUzZTM0NjE4OWI5YjRmY2FhYTk0MDQwZjVhYmZmMTMzOWNkNDU5NTI0YmI2YjRjOWE5Njg1MDYxMzQyYTM1OWRkMTdhN2QyYmVjOTYwYmI1NjRiN2RlODQ3OGNlN2ZiZWIxZDMwZDI2ZmJiNjcxYWRlMzczZjE4ZGNjODFjMWM4OTlkZDUwYThiNGRlMzlmMjc0MGQ5ZjBlY2E3MDE3ZDA5ZTJhNTJkYTIyYjgzMGJhYTUwZDExNTA0N2U5NjE5MjVmNmRjNTRjMDYxYzQ1MWE="}' \
        // --proxy http://localhost:9090


        //"openId": "RTREODRFNTQ0RTk2RUE4MERGRTBEMUMyRjEwOTk4MEFBQjZFRTE1MkY5MDNENUNEM0E2MUJBODg1RUMzMDEzNg==",
        //     "signtimestamp": 1750743749715,
        //     "activeIndex": "cfbdb09fea714337b55ced5bdfe5a8de",
        //     "isOpenContinuePackage": 1,
        //     "phoneNumber": "17816094409",
        //     "backurl": "https://m.woread.com.cn/activity_h5/monthMarket?id=cfbdb09fea714337b55ced5bdfe5a8de&wokey=a26e7a1efa5a00e1c9cdae1b07e6ed9d&pageid=f2b4228f14d74b37b752c35494316afa",
        //     "source": "9",
        //     "channelId": 18002150,
        //     "timestamp": "20250624134229"}
        //{"openId": "RTREODRFNTQ0RTk2RUE4MERGRTBEMUMyRjEwOTk4MEFBQjZFRTE1MkY5MDNENUNEM0E2MUJBODg1RUMzMDEzNg==",
        //     "signtimestamp": 1750744470470,
        //     "activeIndex": "cfbdb09fea714337b55ced5bdfe5a8de",
        //     "isOpenContinuePackage": 1,
        //     "phoneNumber": "17816094409",
        //     "backurl": "https://m.woread.com.cn/activity_h5/monthMarket?id=cfbdb09fea714337b55ced5bdfe5a8de&wokey=a26e7a1efa5a00e1c9cdae1b07e6ed9d&pageid=f2b4228f14d74b37b752c35494316afa",
        //     "source": "9",
        //     "channelId": 18002150,
        //     "timestamp": "20250624135430"}


        JSONObject sourceBody = new JSONObject();
        // 这个openId是登录后获取的 破解版本是拿不到用户的 openid 的
        sourceBody.put("openId", null);
        sourceBody.put("signtimestamp", signTimestamp);
        sourceBody.put("activeIndex", activeIndex);
        sourceBody.put("isOpenContinuePackage", 1);
        sourceBody.put("phoneNumber", phone);
        sourceBody.put("backurl", pageUrl);
        sourceBody.put("source", "9");
        // 写死的好像
        sourceBody.put("channelId", channelId);
        sourceBody.put("timestamp", timestamp);


        String sign = encodeAes2(sourceBody.toJSONString(), aesKey, prodConfig.getIvStr());
        JSONObject postBody = new JSONObject();
        postBody.put("sign", sign);
        final String url = "https://m.woread.com.cn/api/union/pay/wechat/thirdPartyActivity";
        HttpRequest post = HttpRequest.post(url);
        post.header(Header.USER_AGENT.getValue(), USER_AGENT);
        post.header(Header.ACCEPT.getValue());
        post.header("accesstoken", accessToken);
        post.header(Header.ORIGIN.getValue(), ORIGIN_VAL);
        post.header(Header.REFERER.getValue(), pageUrl);
        post.header(Header.ACCEPT_LANGUAGE.getValue(), "zh-CN,zh;q=0.9,en;q=0.8");
        post.header(Header.CONTENT_TYPE.getValue(), ContentType.JSON.getValue());

        post.body(postBody.toJSONString());
        log.info("thirdPartyActivity请求: {}", post);
        // 发送请求并获取响应
        String response = post.execute().body();
        log.info("thirdPartyActivity 响应: {}", response);
        return JSON.parseObject(response, PrepayResp.class);
    }

    /**
     * AES解密方法
     * 注意该方法是固定iv
     *
     * @param data   原始加密字符串 (经过Hex编码后再进行Base64编码的字符串)
     * @param aesKey 密钥字符串
     * @return 解密后的字符串
     */
    private String decodeAes2(String data, String aesKey, String iv) {

        String hexStr = Base64.decodeStr(data, CharsetUtil.CHARSET_UTF_8);
        AES aes = new AES(
                Mode.CBC,
                Padding.PKCS5Padding,
                aesKey.getBytes(StandardCharsets.UTF_8),
                iv.getBytes(StandardCharsets.UTF_8)
        );
        return aes.decryptStr(hexStr, CharsetUtil.CHARSET_UTF_8);
    }

    /**
     * AES 加密方法
     *
     * @param data   要加密的原始字符串
     * @param aesKey 密钥 (必须是16、24或32字节长度)
     * @return Base64编码的加密字符串
     */
    private String encodeAes2(String data, String aesKey, String iv) {
        // 创建AES实例
        AES aes = new AES(
                Mode.CBC,
                Padding.PKCS5Padding,
                aesKey.getBytes(StandardCharsets.UTF_8),
                iv.getBytes(StandardCharsets.UTF_8)
        );
        return Base64.encode(aes.encryptHex(data));
    }

    /**
     * //{
     * //     "code": "0000",
     * //     "innercode": "0000",
     * //     "message": "success",
     * //     "data": {
     * //         "accesstoken": "NTI4RDdGMUU0NUM2QzUzOUM2QTQ2MTc4"
     * //     },
     * //     "success": true
     * // }
     */
    @Data
    public static class AccessTokenResp {

        private String code;
        private String innercode;
        private String message;
        private TokenContent data;
        private boolean success;

        @Data
        public static class TokenContent {
            private String accesstoken;
        }

    }

    /**
     * {"code":"0000","innercode":"0000","message":"success","data":{"mweb_url":"https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx25111621230801edd304f60f1a24560001&package=3091135901","reqUrl":"https://woread.com.cn/capweb/order/levelpage/72cb2ab3f7704adc8686445f528e2a2aa732eb13f9bd45438438e1101ba0e317/w.html"},"success":true}
     */
    @Data
    public static class PrepayResp {

        private String code;
        private String innercode;
        private String message;
        private PrepayContent data;
        private Boolean success;

        @Data
        public static class PrepayContent {
            private String mweb_url;
            private String reqUrl;
        }

    }

    /**
     * demo：
     * {"code":"0000","message":{"creator":2013,"createTime":"20240925164111","name":"悦享铂金VIP","updator":1935,"auditStatus":3,"pagekeyid":"f2b4228f14d74b37b752c35494316afa","updateTime":"20250626111643","pageUrl":"https://m.woread.com.cn/activity_h5/monthMarket?id=cfbdb09fea714337b55ced5bdfe5a8de&channelid=15796641","channelName":"杭州首游触点运营-微信","channelId":"15796641","key":"0f1071eafb2638c889dba6c77d740186"},"innercode":"0000"}
     */
    @Data
    public static class ThirdPartyActivityResp {

        private String code;
        private String innercode;
        private MessageData message;

        @Data
        public static class MessageData {
            private Integer creator;
            private String createTime;
            private String name;
            private Integer updator;
            private Integer auditStatus;
            private String pagekeyid;
            private String updateTime;
            private String pageUrl;
            private String channelName;
            private String channelId;
            // wokey 动态参数
            private String key;
        }
    }
}
