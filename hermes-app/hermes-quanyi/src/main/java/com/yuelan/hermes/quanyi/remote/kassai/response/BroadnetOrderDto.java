package com.yuelan.hermes.quanyi.remote.kassai.response;

import lombok.Data;

/**
 * 下单响应
 */
@Data
public class BroadnetOrderDto {

    /**
     * true 需要实名认证 认证不成功  false 不需要实名认证 认证成功
     */
    private Boolean realNameAuth;

    /**
     * 当值唯为1时表示收单场景 最终认证结果以消息通知为准
     * =1不是真实订单号 要等待回调
     * 不是=1 表示是真实订单号
     */
    private String commonOrderId;

    /**
     * 订购号码
     */
    private String orderMobile;

}
