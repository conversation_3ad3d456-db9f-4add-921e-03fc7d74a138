package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;

/**
 * <AUTHOR> 2024/5/17 下午4:02
 */
public interface EccOuterChannelDOMapper extends BaseMapper<EccOuterChannelDO> {
    /**
     * 根据名字查询渠道
     */
    default EccOuterChannelDO selectByName(String channelName) {
        return selectOne(Wrappers.<EccOuterChannelDO>lambdaQuery()
                .eq(EccOuterChannelDO::getChannelName, channelName));
    }
}