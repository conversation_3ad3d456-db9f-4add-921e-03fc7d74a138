package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.MerchantAccessDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface MerchantAccessMapper extends BaseMapper<MerchantAccessDO> {
    MerchantAccessDO findByMerchantId(@Param("merchantId") Long merchantId);

    List<MerchantAccessDO> findByMerchantIdIn(@Param("merchantIds") Collection<Long> merchantIds);
}