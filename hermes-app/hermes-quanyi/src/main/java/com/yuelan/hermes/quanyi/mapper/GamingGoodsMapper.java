package com.yuelan.hermes.quanyi.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingGoodsDO;
import com.yuelan.hermes.quanyi.controller.request.GamingGoodsReq;
import com.yuelan.hermes.quanyi.controller.response.GamingGoodsSearchRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GamingGoodsMapper extends MPJBaseMapper<GamingGoodsDO> {
    int batchInsert(@Param("list") List<GamingGoodsDO> list);

    List<GamingGoodsSearchRsp> search(@Param("goodsId") Long goodsId, @Param("goodsName") String goodsName);

    List<GamingGoodsDO> findByProductId(@Param("productId") Long productId);

    List<GamingGoodsDO> pageByGamingGoodsReq(@Param("req") GamingGoodsReq req);

    Long countByGamingGoodsReq(@Param("req") GamingGoodsReq req);

    GamingGoodsDO findByGoodsId(Long goodsId);

    int updateStatus(@Param("goodsId") Long goodsId, @Param("status") Integer status);

    int removeById(Long goodsId);

    List<GamingGoodsDO> listOptions(GamingGoodsReq req);
}