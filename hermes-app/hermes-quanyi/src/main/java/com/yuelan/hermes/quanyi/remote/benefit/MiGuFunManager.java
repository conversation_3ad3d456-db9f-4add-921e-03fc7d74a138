package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.MiGuFunPayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.properties.MiGuFunProperties;
import com.yuelan.plugins.redisson.util.RedisUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Scanner;

/**
 * <AUTHOR> 2025/4/15
 * @since 2025/4/15
 * <p>
 * 全街河北
 */
@Slf4j
@Service
@AllArgsConstructor
public class MiGuFunManager {
    private static final String AES_KEY = "KV4lJCt3X3gyKiZ+QCEjKA==";
    private static final String AES_IV = "IyZAYV94K3IoJSpefiEpJA==";
    private static final String SM2_PUBLIC_KEY_HEX = "04f097212e63d49c0189c9df6c78903a7ff353ee5eb1563ac410777be22a4c2193e81c5b16c3048b36598057cd07b352754e9f613e8d2cf3e891eba5751e0d6f30";

    private final MiGuFunProperties miGuFunProperties;

    public static void main(String[] args) {
        new MiGuFunManager(null).test();

    }

    /**
     * 发送短信验证码
     *
     * @param phone   手机号
     * @param pkgEnum 枚举包
     * @return 是否成功
     */
    public boolean sendSmsCode(String phone, MiGuFunPayPkgEnum pkgEnum) {
        try {
            MiGuFunProperties.ProdConfig prodConfig = miGuFunProperties.getProdConfigMap().get(pkgEnum);
            String moduleId = prodConfig.getModuleId();
            Assert.notNull(moduleId, "moduleId 没有读取到");
            JSONObject provinceMarketFloors = queryProvinceMarketFloors(moduleId);
            String relatePropsId = parseRelatePropsId(provinceMarketFloors);
            Assert.notNull(relatePropsId, "relatePropsId 请求失败");
            JSONObject sendSmsResult = sendMsgCode(phone, moduleId, relatePropsId);
            boolean successResp = isSuccessResp(sendSmsResult);
            if (successResp) {
                cacheRelatePropsId(phone, relatePropsId);
            }
            return successResp;
        } catch (Exception e) {
            log.error("发送验证码异常", e);
            return false;
        }
    }

    /**
     * 验证短信验证码
     *
     * @param phone   手机号
     * @param smsCode 短信验证码
     * @param pkgEnum 枚举包
     * @return 是否成功
     */
    public BenefitPayResultBO verifySmsCode(String phone, String smsCode, MiGuFunPayPkgEnum pkgEnum) {
        try {
            MiGuFunProperties.ProdConfig prodConfig = miGuFunProperties.getProdConfigMap().get(pkgEnum);
            String moduleId = prodConfig.getModuleId();
            Assert.notNull(moduleId, "moduleId 没有在配置没居中读到");
            String relatePropsId = getRelatePropsId(phone);
            Assert.notNull(relatePropsId, "relatePropsId 没有在缓存中获取到");
            JSONObject createOrderResult = createOrder(phone, smsCode, moduleId, relatePropsId);
            if (!isSuccessResp(createOrderResult)) {
                log.error("验证验证码失败, 手机号: {},-返回结果: {}", phone, createOrderResult);
                String message = createOrderResult.getString("message");
                message = StrUtil.isEmpty(message) ? "订购失败" : message;
                return BenefitPayResultBO.fail(message);
            }
            return BenefitPayResultBO.success();
        } catch (Exception e) {
            log.error("验证验证码异常", e);
            return BenefitPayResultBO.fail("系统开小差了，请稍后再试");
        }
    }

    /**
     * 缓存一下参数提交验证码时候用
     *
     * @param phone         手机号码
     * @param relatePropsId 动态获取的参数
     */
    public void cacheRelatePropsId(String phone, String relatePropsId) {
        String cacheKey = getCommCacheKey(phone);
        RedisUtils.setCacheObject(cacheKey, relatePropsId, Duration.ofMinutes(30));
    }

    /**
     * 获取 relatePropsId
     * 说明：relatePropsId应该是moduleId 一一对应的请求内可以获取到，但是提交验证码时候不应该重新获取没必要多发送不必要的请求
     *
     * @param phone 手机号码
     * @return 动态获取的参数
     */
    public String getRelatePropsId(String phone) {
        String cacheKey = getCommCacheKey(phone);
        Object value = RedisUtils.getCacheObject(cacheKey);
        if (value == null) {
            log.error("加载缓存失败，手机号: {},-请重新发送验证码", phone);
            return null;
        }
        return value.toString();
    }

    private String getCommCacheKey(String phone) {
        return RedisKeys.getMiguFunCache(phone);
    }

    /**
     * 检查响应是否成功
     *
     * @param respJson 响应的JSON对象
     * @return 如果返回码为"000000"则返回true，否则返回false
     */
    private boolean isSuccessResp(JSONObject respJson) {
        String returnCode = respJson.getString("returnCode");
        return Objects.equals(returnCode, "000000");
    }

    /**
     * 构建POST请求的源数据
     *
     * @param postUrl  请求URL
     * @param dataJson 请求数据
     * @return 封装好的请求JSON对象，包含headers、method、url和data
     */
    private JSONObject buildPostSourceData(String postUrl, JSONObject dataJson) {
        final String appType = "0";
        final String platform = "1";
        final String userId = "";
        final String userToken = "";
        final String headerSig = "";
        final String appversion = "1.0.9.7";
        final String appChannel = "zy-gw-mghy";

        return new JSONObject() {{
            put("headers", new JSONObject() {{
                put("appType", appType);
                put("platform", platform);
                put("userId", userId);
                put("userToken", userToken);
                put("headerSign", headerSig);
                put("appversion", appversion);
                put("appChannel", appChannel);
            }});
            put("method", "post");
            put("url", postUrl);
            put("data", dataJson);
        }};
    }

    /**
     * 查询关联信息
     *
     * @param moduleId 模块ID
     * @return 查询结果的JSON对象
     */
    private JSONObject queryProvinceMarketFloors(String moduleId) {
        final String url = "https://betagame.migufun.com/member/provincemarket/v1.0.8.9/queryProvinceMarketFloors";
        JSONObject dataJson = new JSONObject() {{
            put("moduleId", moduleId);
        }};
        return commPost(dataJson, url);
    }

    /**
     * 发送短信验证码
     *
     * @param phone         手机号码（将被SM2加密）
     * @param moduleId      模块ID
     * @param relatePropsId 关联属性ID
     * @return 发送结果的JSON对象
     */
    private JSONObject sendMsgCode(String phone, String moduleId, String relatePropsId) {
        final String url = "https://betagame.migufun.com/member/provincemarket/v1.0.8.9/sendMsgCode";

        JSONObject dataJson = new JSONObject() {{
            put("user", sm2Encrypt(phone));
            put("moduleId", moduleId);
            put("relatePropsId", relatePropsId);
            put("subchannelId", "");
        }};
        return commPost(dataJson, url);
    }

    /**
     * 验证短信验证码
     *
     * @param phone         手机号码（将被SM2加密）
     * @param smsCode       短信验证码
     * @param moduleId      模块ID
     * @param relatePropsId 关联属性ID
     * @return 验证结果的JSON对象
     */
    private JSONObject createOrder(String phone, String smsCode, String moduleId, String relatePropsId) {
        final String url = "https://betagame.migufun.com/member/provincemarket/v1.0.8.9/createOrder";
        JSONObject dataJson = new JSONObject() {{
            put("user", sm2Encrypt(phone));
            put("moduleId", moduleId);
            put("relatePropsId", relatePropsId);
            put("msgCode", sm2Encrypt(smsCode));
        }};
        return commPost(dataJson, url);
    }

    /**
     * 通用POST请求方法
     * 执行流程：
     * 1. 构建请求源数据
     * 2. 加密请求体
     * 3. 处理并加密请求头
     * 4. 发送请求并获取响应
     * 5. 解密响应数据
     *
     * @param reqBody 请求体JSON对象
     * @param url     请求URL
     * @return 解密后的响应JSON对象
     */
    private JSONObject commPost(JSONObject reqBody, String url) {
        String accept = "application/json, text/plain, */*";
        JSONObject sourceData = buildPostSourceData(url, reqBody);
        // 处理发送数据
        sourceData.put("data", aesCbcPkcs7(reqBody.toJSONString()));
        // 处理header
        dealAndEncryptHeader(sourceData, accept);
        // 发送的header
        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", accept);
        headers.put("Accept-Encoding", "gzip, deflate, br, zstd");
        headers.put("Accept-Language", "zh-CN,zh;q=0.9");
        headers.put("Referer", "https://www.migufun.com/");
        headers.put("Origin", "https://www.migufun.com");
        headers.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********");
        // 合并header
        sourceData.getJSONObject("headers").forEach((k, v) -> headers.put(k, v.toString()));
        // 发送请求
        HttpRequest request = HttpRequest.post(url);
        request.addHeaders(headers);
        request.body(sourceData.getString("data"));
        log.info("请求: {}", request);
        HttpResponse execute = request.execute();
        log.info("响应: {}", execute);
        // 响应数据
        String body = execute.body();
        JSONObject respJson = JSONObject.parseObject(aesCbcPkcs7Decrypt(body));
        log.info("响应数据: {}", respJson);
        return respJson;
    }

    /**
     * 处理并加密请求头
     *
     * @param sourceData 源数据JSON对象
     * @param accept     Accept请求头值
     */
    private void dealAndEncryptHeader(JSONObject sourceData, String accept) {
        JSONObject headers = sourceData.getJSONObject("headers");
        headers.put("timestamp", System.currentTimeMillis() / 1000);
        headers.put("Content-Type", "application/json");

        // 先合并再加密
        JSONObject axiosHeader = axiosHeader(accept);
        JSONObject mergeAxiosHeader = headerMerge(headers, axiosHeader);

        headers.put("mgheaders", aesCbcPkcs7(mergeAxiosHeader.toJSONString()));
        headers.put("peekaboo", "1");
        headers.put("hgv", "5rPulhA0y");
    }

    /**
     * 构建Axios风格的请求头
     *
     * @param accept Accept请求头值
     * @return Axios风格的请求头JSON对象
     */
    private JSONObject axiosHeader(String accept) {
        return new JSONObject() {{
            put("common", new JSONObject() {{
                put("Accept", accept);
            }});
            put("delete", new JSONObject());
            put("get", new JSONObject());
            put("head", new JSONObject());
            put("post", new JSONObject() {{
                put("Content-Type", "application/x-www-form-urlencoded");
            }});
            put("put", new JSONObject() {{
                put("Content-Type", "application/x-www-form-urlencoded");
            }});
            put("patch", new JSONObject() {{
                put("Content-Type", "application/x-www-form-urlencoded");
            }});
        }};
    }

    /**
     * 合并请求头
     *
     * @param header      原始请求头
     * @param axiosHeader Axios风格的请求头
     * @return 合并后的请求头JSON对象
     */
    private JSONObject headerMerge(JSONObject header, JSONObject axiosHeader) {
        JSONObject result = new JSONObject();
        result.putAll(axiosHeader);
        result.putAll(header);
        return result;
    }

    /**
     * 从响应中解析RelatePropsId
     *
     * @param respJson 响应JSON对象
     * @return 如果找到则返回RelatePropsId，否则返回null
     */
    private String parseRelatePropsId(JSONObject respJson) {
        String returnCode = respJson.getString("returnCode");
        if (!Objects.equals(returnCode, "000000")) {
            return null;
        }
        JSONObject resultData = respJson.getJSONObject("resultData");
        JSONArray floors = resultData.getJSONArray("floors");
        for (int i = 0; i < floors.size(); i++) {
            JSONObject floor = floors.getJSONObject(i);
            String relatePropsId = floor.getString("relatePropsId");
            if (StrUtil.isNotEmpty(relatePropsId)) {
                return relatePropsId;
            }
        }
        return null;
    }

    /**
     * AES-CBC-PKCS7模式加密
     * 注意：Java中的PKCS5Padding等同于JavaScript中的PKCS7Padding
     *
     * @param data 待加密数据
     * @return Base64编码的加密字符串
     */
    private String aesCbcPkcs7(String data) {
        // java的 PKCS5Padding = js的 PKCS7Padding
        byte[] keyBytes = Base64.decodeBase64(AES_KEY);
        byte[] ivBytes = Base64.decodeBase64(AES_IV);

        AES aes = new AES(
                Mode.CBC,
                Padding.PKCS5Padding,
                keyBytes,
                ivBytes
        );
        byte[] encrypt = aes.encrypt(data.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64String(encrypt);
    }

    /**
     * AES-CBC-PKCS7模式解密
     *
     * @param data Base64编码的加密字符串
     * @return 解密后的原始字符串
     */
    private String aesCbcPkcs7Decrypt(String data) {
        byte[] keyBytes = Base64.decodeBase64(AES_KEY);
        byte[] ivBytes = Base64.decodeBase64(AES_IV);

        AES aes = new AES(
                Mode.CBC,
                Padding.PKCS5Padding,
                keyBytes,
                ivBytes
        );
        byte[] decrypt = aes.decrypt(Base64.decodeBase64(data));
        return new String(decrypt, StandardCharsets.UTF_8);
    }

    /**
     * SM2加密（国密算法）
     * 使用C1C3C2模式
     *
     * @param data 待加密数据
     * @return 十六进制格式的加密字符串
     */
    private String sm2Encrypt(String data) {
        SM2 sm2 = SmUtil.sm2(null, SM2_PUBLIC_KEY_HEX);
        sm2.setMode(SM2Engine.Mode.C1C3C2);

        // hex格式
        return sm2.encryptHex(data, KeyType.PublicKey);
    }

    private void test() {

        // System.out.println(aesCbcPkcs7Decrypt("PgJfVu5l3uPLuJUkCw18TQ+120/afzNvHRDe/VHIOdPIVusmESuO/3RD0BwZr5P5J726V0oHIJA4TNqNpitXX1+qYt42ElCNYpGfzmiSGh2xYMMsXELAn9x+qNO2m8MtMWyp5lMAndrlwflW4gPd3oHn+Lq8MyIM76+PcSSdzZjDTUQjU5BFhOFO2Wp7HBL/RVBOXhRgNSq6aIAk564LMmVtT1TAdBhi94oJuIkfl7HqbojXkPotEy2ulqzlHKp1yGWyXQ8eXMQo+BVZO72kQmZXyfapuiY3/PN3ZPUSwK/jGBrOrjr97iRLg7cvyYRwWsHfL3OfF0+AnJldfEkZPHk/rEAzeb/lgf3gxp5SIzAktzO9WCgDv1GMoNPGLUkElF2VMUOptTIh1Hhdf8L0qxXGkeOSBSB0qo1OGlqFJafUEUh35GzBd5r4eDC0oxtIIcJiVQrrTk0qcRVUvfA9vGOI1SbWwwpkSfNw0YNy1b4kHEAXHHFA8U+JmuMCuqH5HSI5i/GiF0gHFc+oF0r4F0DmWG4DVE64mKV5/S62yES+ymtZpYaeLlnEueb3nwYrAH1AI7hoF/ZWxVTcmE3GAl63vNNU4yGZo0AA9S8IbJIpdetpIPnv1nCA5TK5HvVyHDFhiiSQLM4HEFbdMTsOHA=="));

        String moduleId = "156036";
        JSONObject provinceMarketFloors = queryProvinceMarketFloors(moduleId);
        String relatePropsId = parseRelatePropsId(provinceMarketFloors);
        if (relatePropsId == null) {
            log.error("moduleId 没获取到");
            return;
        }
        System.err.println("moduleId = " + moduleId);
        // System.out.println(aesCbcPkcs7Decrypt(
        //         "FaDuFvJY3837NmqhLnp9+Uqqp3pFxW8/t1rCtoe1Lov90UuulRvWEeKnx+/aQjiFR7K2JiF3uNgUnhQgjK0Eh1hvYiUR+htf+u2gTj+3HnWwQWZU7ujIg9wDCDBXv/sFqvOpCrPlMOgtsruvd7aVkYT3T6+j4kWOaxuZcsxGo3M6n2mGY8Rd1NpNyNM4zEkd4ohIzsnZY4LPTqDyTW2aDLTlmX9cQbH9XExvDG+wdxFVf4VHNM/89PbEFI4PmJ/cqi8uV4EZ4kG/GnChzKa4q2oHT7S8k+yWIaw2oq3ro9O8LWHcdtbfOzR6hSN8RCkRtXd+IUh+Q6k6ka5qAJIti88w/0mAnZE/pwTkyNB6gSCBVFKFW2u6B6vmK1rfK+jlYZMnik+v7CE1+GdAm28fWS3G0KZQnHVDdh8HV3YImZZeWdg4HCO64jSeQEfA/S5IRwaQvvmlHeOIx3LoQ+UPDTA5R2GlE9hl1E/EhbaUQeRdXvHlKQ11hCnsIfy7vIRPLqFIOC/1aXo1DiUDl6y3Y43vbAKBjjmzoNcLVBfUwbbtTbikq6yRr5tXw5qxYl+dhPHZlicqIVEhqeqIVuvylg=="));
        String mobile = "15732521753";
        System.out.println(sendMsgCode(mobile, moduleId, relatePropsId));

        // System.out.println(aesCbcPkcs7Decrypt("Gf3o0Lyq8vEBOI53MpAKaoJIeLaWc+QXL/RHfSrYvt+jThBXB9E5otnp/YCSox4vhtNNnGEfwYc7nk7pdrTN3NawcQe+nGRLJoVylcwQWxOUVbtjFLs7CbkvraF2mKJSMKejihAk6+mYPsL7ccl7tTVcOS5PRaCPOSrOHQgB6ZGxXRqdDyHNZ26Tg++ywPojcsWj2dlr3+4lq9DH2/0x1i1YTcyU2H7Dlhip6MjxY27sU22T1Bz5JB2DoL1KgetWk0XhblIKeAGRWZI2LVUxxPVuSRZz33hxayxVMKJ+OjPl+ulkf0/djjd5VQbm9HgCxyMn3HDGZ4+I5jk2tChQYMJ2A7aQphpSIGYViaDrSIeC70P2DoGUSZC4YckiowSbPxhR2sk03UszWoooOgo3gpJe2hdyao+l906vN9Rg/oo03oeFTj414JM/lhUYWZuEt2eTphd5AzcGEIA4vrFwvtHbZAOZz92+4+GUA/dKVobUp+fC0gBTds3mrr3PG8IUn7y9DpsMSB0sfOQGN1PpfSnCKlkMB1EAtWz6C7mmYnsam2qCMfS3mw+oIPlLRfB8ScG0jKnpAfrDiInFfpaxgYc6ucYmXoBOSosPEPaZ9PNaktD4+Ne2E6zAMcVAJSSBbrcN5tDQJlrUlCvRxAaydg=="));
        Scanner scanner = new Scanner(System.in);
        System.out.print("输入验证码: ");
        String smsCode = scanner.nextLine();
        System.out.println("输入的验证码: " + smsCode);
        System.out.println(createOrder(mobile, smsCode, moduleId, relatePropsId));
    }
}
