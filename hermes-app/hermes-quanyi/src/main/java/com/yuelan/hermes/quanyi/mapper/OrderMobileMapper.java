package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.OrderMobileDO;
import com.yuelan.hermes.quanyi.controller.request.OrderMobileListReq;
import com.yuelan.hermes.quanyi.controller.request.RechargerOrderDetailReq;
import com.yuelan.hermes.quanyi.controller.response.RechargerOrderDetailRsp;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OrderMobileMapper extends BaseMapper<OrderMobileDO> {
    OrderMobileDO findByOrderNo(String orderNo);

    OrderMobileDO findByOutTradeNoAndMchId(@Param("outTradeNo") String outTradeNo, @Param("mchId") String mchId);

    RechargerOrderDetailRsp findByRechargerOrderDetailReq(RechargerOrderDetailReq req);

    Long countByOrderMobileListReq(@Param("req") OrderMobileListReq req);

    List<OrderMobileDO> pageByOrderMobileListReq(@Param("req") OrderMobileListReq req);

    List<OrderMobileDO> cursorByOrderMobileListReq(@Param("req") OrderMobileListReq req, @Param("maxId") Long maxId);

    int updateItemNo(@Param("orderId") Long orderId, @Param("itemNo") String itemNo, @Param("orderStatus") Integer orderStatus);

    int updateNotifyStatus(@Param("orderId") Long orderId, @Param("notifyStatus") Integer notifyStatus);

    int updateStatus(@Param("orderId") Long orderId, @Param("orderStatus") Integer orderStatus, @Param("finishTime") Date finishTime,
                     @Param("voucher") String voucher, @Param("sp") Integer sp);

    Integer existByAbnormal();
}