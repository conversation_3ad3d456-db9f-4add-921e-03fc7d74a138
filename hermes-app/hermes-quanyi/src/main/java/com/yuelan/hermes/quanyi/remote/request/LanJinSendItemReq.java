package com.yuelan.hermes.quanyi.remote.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 蓝鲸电竞卡 - 发放道具请求
 *
 * <AUTHOR> Generated
 * @since 2025-07-10
 */
@Data
public class LanJinSendItemReq {

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String account;

    /**
     * 道具类型（dian_jing_ka_12：12块礼包，dian_jing_ka_15：15块礼包）
     */
    @Schema(description = "道具类型（dian_jing_ka_12：12块礼包，dian_jing_ka_15：15块礼包）")
    private String cardtype;

    @Schema(description = "订单号")
    private String order;

    /**
     * 渠道标识
     */
    @Schema(description = "渠道标识")
    private String channel;

    /**
     * 时间戳（秒为单位，10分钟过期）
     */
    @Schema(description = "时间戳（秒为单位，10分钟过期）")
    private String stamp;

    /**
     * 签名
     */
    @Schema(description = "签名")
    private String sign;
}
