package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;

@Data
public class SxfOrderQueryReq {
    /**
     * 商户编号
     */
    private String mno;
    /**
     * 商户订单号（字母、数字、下划线）
     * 与uuid，transactionId，sxfUuid字段四选一必传
     */
    private String ordNo;
    /**
     * 天阙平台订单号
     * 与ordNo，transactionId，sxfUuid字段四选一必传
     */
    private String uuid;
    /**
     * 支付宝或微信单号
     * 与uuid，ordNo，sxfUuid字段四选一必传
     */
    private String transactionId;
    /**
     * 落单号
     * 与uuid，ordNo，transactionId字段四选一必传
     * 消费者账单中的条形码订单号
     */
    private String sxfUuid;

}
