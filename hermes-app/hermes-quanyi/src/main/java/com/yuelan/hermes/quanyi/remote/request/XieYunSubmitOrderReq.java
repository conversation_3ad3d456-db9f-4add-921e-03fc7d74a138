package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2024/12/5
 * @since 2024/12/5
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XieYunSubmitOrderReq extends XieYunSubmitPreCheckReq {
    /**
     * 快递-市
     */
    private String areaName;

    /**
     * 区县代码（到区级）
     */
    private String regionCode;

    /**
     * 号码地市编码
     */
    private String cityCode;

    /**
     * 号码地市名称
     */
    private String cityName;

    /**
     * 一证通查照片标识（下单号 码归属地市为深圳时必传）
     */
    private String oneCardCheck;

    /**
     * 社保或居 住证照片
     * 社保或居住证照片标识（下 单号码归 属地市为深圳时 必传）
     */
    private String socialSecurity;
}


