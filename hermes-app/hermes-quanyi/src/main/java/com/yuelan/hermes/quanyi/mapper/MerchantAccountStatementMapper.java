package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.MerchantAccountStatementDO;
import com.yuelan.hermes.quanyi.controller.request.MchAccountStatementReq;
import com.yuelan.hermes.quanyi.controller.response.MchAccountStatementRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MerchantAccountStatementMapper extends BaseMapper<MerchantAccountStatementDO> {
    List<MerchantAccountStatementDO> findByBizNo(@Param("bizNo") String bizNo, @Param("bizType") Integer bizType);

    List<MchAccountStatementRsp> pageByMchAccountStatementReq(MchAccountStatementReq req);

    Long countByMchAccountStatementReq(MchAccountStatementReq req);
}