package com.yuelan.hermes.quanyi.remote.jinxi.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 今溪订单充值请求
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JinXiOrderRechargeReq extends JinXiBaseReq {

    /**
     * 购买数量
     */
    private Integer buyNumber;

    /**
     * 客户订单号
     */
    private String customOrderId;

    /**
     * 自定义透传参数，格式不限（500字符以内）
     */
    private String external;

    /**
     * 商品编号
     */
    private String productId;

    /**
     * 订购号码（卡密类型商品可不传该字段）
     */
    private String rechargeAccount;

    /**
     * 充值模板
     */
    private String rechargeTemplate;

    /**
     * 运营商话费订购产品需要填写验证码
     */
    private String verifityCode;
}
