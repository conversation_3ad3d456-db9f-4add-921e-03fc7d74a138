package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> 2024/5/17 下午4:05
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccOuterChannelListReq extends PageRequest {

    @Schema(description = "外部渠道id")
    public Long outerChannelId;

    @Schema(description = "渠道名字")
    private String channelName;

    @Schema(description = "对接参数apiKey")
    private String apiKey;


    public Wrapper<EccOuterChannelDO> buildQueryWrapper() {
        return Wrappers.lambdaQuery(EccOuterChannelDO.class)
                .eq(outerChannelId != null, EccOuterChannelDO::getOuterChannelId, outerChannelId)
                .like(channelName != null, EccOuterChannelDO::getChannelName, channelName)
                .eq(apiKey != null, EccOuterChannelDO::getApiKey, apiKey)
                .orderByDesc(EccOuterChannelDO::getOuterChannelId);
    }

}
