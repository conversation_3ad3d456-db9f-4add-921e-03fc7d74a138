package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.PhoneAffiliationDO;

public interface PhoneAffiliationMapper extends BaseMapper<PhoneAffiliationDO> {

//    @Select("select * from phone_affiliation where deleted = 0")
//    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 500)
//    @ResultType(PhoneAffiliationDO.class)
//    void dynamicSelectLargeData(ResultHandler<PhoneAffiliationDO> handler);
}