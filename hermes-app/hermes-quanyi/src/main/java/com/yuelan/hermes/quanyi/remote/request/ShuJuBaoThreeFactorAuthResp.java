package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;

/**
 * <AUTHOR> 2025/6/6
 * @since 2025/6/6
 */
@Data
public class ShuJuBaoThreeFactorAuthResp {

    private String code;

    private String message;

    private RespData data;

    private String seqNo;

    public boolean bizSuccess() {
        return "10000".equals(code) && "1".equals(data.state);
    }

    public boolean apiSuccess() {
        return "10000".equals(code);
    }

    @Data
    public static class RespData {
        /**
         * 原运营商(1:移动，2：联通，3：电信)
         */
        private String operatorReal;
        /**
         * 验证结果（1:验证一致，2:验证不一致，3:异常情 况）
         */
        private String state;
        /**
         * 是否携号转网(0：否,1：是)
         */
        private String isXhzw;
        /**
         * 当前所属运营商(1:移动，2：联通，3：电信)
         */
        private String operator;
    }

}
