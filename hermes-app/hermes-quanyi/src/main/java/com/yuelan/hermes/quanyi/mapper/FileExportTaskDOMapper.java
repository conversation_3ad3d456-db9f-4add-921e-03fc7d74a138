package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.FileExportTaskDO;

import java.util.List;

/**
 * <AUTHOR> 2024/7/9 下午7:34
 */
public interface FileExportTaskDOMapper extends BaseMapper<FileExportTaskDO> {
    default List<FileExportTaskDO> listByTaskStatus(Integer taskStatus) {
        return selectList(Wrappers.<FileExportTaskDO>lambdaQuery()
                .eq(FileExportTaskDO::getTaskStatus, taskStatus)
                .orderByAsc(FileExportTaskDO::getTaskId));
    }

    default List<FileExportTaskDO> listByWriteStatusAndWaitingUpload(Integer writeStatus, Integer uploadStatus) {
        return selectList(Wrappers.<FileExportTaskDO>lambdaQuery()
                .eq(FileExportTaskDO::getFileWriteStatus, writeStatus)
                .eq(FileExportTaskDO::getOssUploadStatus, uploadStatus)
                .orderByAsc(FileExportTaskDO::getTaskId));
    }
}