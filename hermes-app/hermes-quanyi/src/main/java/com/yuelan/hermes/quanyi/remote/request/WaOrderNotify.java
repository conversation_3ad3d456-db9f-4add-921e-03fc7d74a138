package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;

/**
 * 我爱号码网-订单通知
 * <p>
 * <strong>状态码说明：</strong><br>
 * <pre>     1:局方下单成功</pre>
 * <pre>     2:局方下单失败</pre>
 * <pre>     3:订单激活</pre>
 * <pre>     4:订单发货，更新物流单号</pre>
 * <pre>     5:更新订单首充金额</pre>
 * <pre>     6:退单，局方下单成功后订单退单</pre>
 * <p>
 * <strong>说明：</strong><br>
 * <pre>     状态码1和2会有重复回调的可能，请做好逻辑判断</pre>
 * <pre>     订单可能局方进单失败后，推送失败状态给接入方，之后我方客服介入跟单后，订单会进单成功，此时会再次推送下单成功状态给接入方。</pre>
 *
 * <AUTHOR> 2024/11/20
 * @since 2024/11/20
 */
@Data
public class WaOrderNotify {

    /**
     * 下单接口推送过来的订单编号
     */
    private String orderNo;

    /**
     * 订购的手机号码
     */
    private String phoneNumber;

    /**
     * 见下方状态码说明
     */
    private Integer status;

    /**
     * 物流公司，status为4时有值
     */
    private String expressComponey;

    /**
     * 物流单号，status为4时有值
     */
    private String expressNo;

    /**
     * 激活时间，status为3时有值，部分订单会没有激活时间
     */
    private String activeTime;

    /**
     * 首充金额（元），status为5时有值 最多2位小数
     */
    private Double chargeMoney;

    /**
     * 下单失败或者退单原因，status为2或者6时有值
     */
    private String reason;


}
