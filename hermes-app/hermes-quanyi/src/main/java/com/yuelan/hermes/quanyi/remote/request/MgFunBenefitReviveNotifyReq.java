package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;

/**
 * 领取结果异步回调请求对象
 */
@Data
public class MgFunBenefitReviveNotifyReq {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 道具领取时间（13位时间戳）
     */
    private Long receiveTime;

    /**
     * 扩展字段(咪咕互娱透传用)
     */
    private String extrInfo;

    /**
     * 下发成功区服
     */
    private String sendServer;

    /**
     * 下发成功角色
     */
    private String sendRole;

    /**
     * 当前时间（13位时间戳）
     */
    private Long timestamp;


}
