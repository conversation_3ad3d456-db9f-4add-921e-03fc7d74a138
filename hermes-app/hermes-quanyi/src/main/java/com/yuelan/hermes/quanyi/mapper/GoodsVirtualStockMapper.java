package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualStockDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface GoodsVirtualStockMapper extends BaseMapper<GoodsVirtualStockDO> {
    int batchInsert(@Param("list") List<GoodsVirtualStockDO> list);

    int updateRealAmount(@Param("skuId") Long skuId, @Param("realAmount") Integer realAmount);

    List<GoodsVirtualStockDO> findBySkuIdIn(@Param("skuIds") Collection<Long> skuIds);

    GoodsVirtualStockDO findBySkuId(@Param("skuId") Long skuId);

    int deduction(@Param("skuId") Long skuId, @Param("amount") Long amount);

    /**
     * 查询预警库存
     */
    List<Long> cursorByStockAlarm(@Param("minId") Long minId);

    /**
     * 更新统计库存
     */
    int updateStatisticsAmountBySkuId(@Param("skuId") Long skuId,
                                      @Param("realAmount") Integer realAmount,
                                      @Param("sellAmount") Integer sellAmount,
                                      @Param("waitExpiresAmount") Integer waitExpiresAmount,
                                      @Param("expiresAmount") Integer expiresAmount,
                                      @Param("disableAmount") Integer disableAmount);

    int updateDisableAmount(@Param("skuId") Long skuId, @Param("disableAmount") Integer disableAmount);
}