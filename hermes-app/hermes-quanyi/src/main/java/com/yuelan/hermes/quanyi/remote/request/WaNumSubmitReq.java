package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 我爱号码网提交请求
 *
 * <AUTHOR> 2024/11/20
 * @since 2024/11/20
 */
@Accessors(chain = true)
@Data
public class WaNumSubmitReq {
    /**
     * 开户人或者宽带装机人真实姓名
     */
    private String name;

    /**
     * 开户人或者宽带装机人身份证号码
     */
    private String idcard;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 收件或者宽带装机省（暂时只有浙江省内宽带），和地区编码表postProvinceName列对应
     */
    private String province;

    /**
     * 收件或者宽带装机城市，和地区编码表postCityName列对应
     */
    private String city;

    /**
     * 收件或者宽带装机区/县，和地区编码表postDistrictName列对应
     */
    private String district;

    /**
     * 详细地址，不能少于4个字
     */
    private String address;

    /**
     * 订单编号，唯一，长度不能超过32个字符
     */
    private String orderNo;

    /**
     * 下单的产品编码，亿卡汇方提供
     */
    private String goodsCode;

    /**
     * 下单手机号码，选号订单传入，该参数为空时随机号码下单
     */
    private String phoneNumber;

    /**
     * 下单号码的归属省份，广电和移动产品下单必传
     */
    private String phoneProvince;

    /**
     * 下单号码的归属城市，广电和移动产品下单必传
     */
    private String phoneCity;
}
