package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.AppConnectDO;

/**
 * <AUTHOR> 2024/7/29 上午10:57
 */
public interface AppConnectDOMapper extends BaseMapper<AppConnectDO> {
    default AppConnectDO getByBizUserIdAndSocialType(String bizUserId, Integer socialType) {
        return selectOne(Wrappers.<AppConnectDO>lambdaQuery().
                eq(AppConnectDO::getBizUserId, bizUserId)
                .eq(AppConnectDO::getSocialType, socialType));
    }

    default AppConnectDO selectByTempToken(String tempToken) {
        return selectOne(Wrappers.<AppConnectDO>lambdaQuery()
                .eq(AppConnectDO::getTempToken, tempToken));
    }

    default AppConnectDO selectByUserId(Long hshUserId) {
        return selectOne(Wrappers.<AppConnectDO>lambdaQuery()
                .eq(AppConnectDO::getUserId, hshUserId));
    }

    default boolean setUserIdIsNullById(Long id) {
        return new LambdaUpdateChainWrapper<>(this)
                .set(AppConnectDO::getUserId, null)
                .eq(AppConnectDO::getId, id)
                .update();
    }
}