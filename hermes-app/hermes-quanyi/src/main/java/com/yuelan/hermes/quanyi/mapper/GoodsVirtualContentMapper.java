package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualContentDO;
import org.apache.ibatis.annotations.Param;

public interface GoodsVirtualContentMapper extends BaseMapper<GoodsVirtualContentDO> {
    int insert(GoodsVirtualContentDO goodsVirtualContentDO);

    int updateByGoodsId(@Param("content") String content, @Param("goodsId") Long goodsId);

    String findByGoodsId(@Param("goodsId") Long goodsId);
}