package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.enums.HNDxPayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.properties.HaiNanDxProperties;
import com.yuelan.hermes.quanyi.common.util.UopRSAUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.net.HttpCookie;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> 2025/3/20
 * @since 2025/3/20
 * <p>
 * 海南电信
 * 《互联网支撑平台代理商接口文档V1.2.pdf》按照该文档对接并且获取链接以后，拆分出获取验证码和验证验证码两个接口
 */
@Slf4j
@Service
@AllArgsConstructor
public class HaiNanDxManager {
    private static final String TAG = "[海南电信]";
    private static final String REQ_URL = "https://openeop.dcoos.189.cn:8100/serviceAgent/rest/uop-web/executeEnhanced";

    private final HaiNanDxProperties properties;

    public SmsCookieResult sendSmsCode(String mobile, String orderNo, HNDxPayPkgEnum pkgEnum) {
        Set<HttpCookie> cookies = new HashSet<>();
        HaiNanDxProperties.ProdConfig prodConfig = properties.getProdMap().get(pkgEnum);
        String link = getLink(mobile, orderNo, prodConfig);
        if (StringUtils.isEmpty(link)) {
            log.error("{}：获取链接失败", TAG);
            return new SmsCookieResult().setSuccess(false);
        }
        String location = doAutoRedirect(link, cookies);
        if (StringUtils.isEmpty(location)) {
            log.error("{}：获取重定向后地址失败", TAG);
            return new SmsCookieResult().setSuccess(false);
        }
        boolean preSuccess = queryProdDescAndReqImg(location, prodConfig.getProdNbr(), cookies);
        if (!preSuccess) {
            log.error("{}：查询产品描述失败", TAG);
            return new SmsCookieResult().setSuccess(false);
        }
        boolean jsGetSmsCode = jsGetSmsCode(location, prodConfig.getProdNbr(), cookies);

        if (!jsGetSmsCode) {
            return new SmsCookieResult().setSuccess(false);
        }
        Map<String, String> cookieMap = new HashMap<>();
        for (HttpCookie cookie : cookies) {
            cookieMap.put(cookie.getName(), cookie.getValue());
        }
        return new SmsCookieResult().setSuccess(true)
                .setOrderNo(orderNo)
                .setCookies(cookieMap);
    }

    /**
     * 4.1.1代理商获取链接接口
     * 查看接口逻辑可能有线程安全问题，需要加锁
     * 请求的url里面是有手机号码信息的，重定向的地址没有看到手机信息，（请求也可以发送验证码） 也就是说第一步请求是设置号码 第二次重定向请求就是获取上次设置的号卡短信发送掉
     */
    private String getLink(String accNbr, String orderNo, HaiNanDxProperties.ProdConfig prodConfig) {
        final String methodTag = TAG + "-[获取链接]";
        log.info("{}", methodTag);
        final String abilityCode = "salesLinkEnc";
        String prodNbr = prodConfig.getProdNbr();

        HaiNanGetLinkReq req = new HaiNanGetLinkReq();
        req.setAccNbr(accNbr);
        req.setAuthCode("agentSecConfirmPage");
        req.setCode(prodConfig.getCode());
        req.setDevMobile(properties.getDevMobile());
        req.setDevStaffCode(properties.getDevStaffCode());
        req.setProdNbr(prodNbr);
        req.setNotifyType("0");
        req.setSpOpenNo(orderNo);
        String reqJson = JSONObject.toJSONString(req);

        log.info("{}加密前：{}", methodTag, reqJson);

        String transactionId = genTransactionId(abilityCode);
        // 生成请求头X-Auth
        String headAuth = genHeadAuth(reqJson, abilityCode, transactionId);
        String encryptBody = UopRSAUtils.encryptByPublicKey(reqJson, properties.getRsaPublicKey());

        HttpRequest post = HttpRequest.post(REQ_URL);
        post.header("Content-Type", "application/json");
        post.header("X-Auth", headAuth);
        post.header("X-APP-ID", properties.getAppId());
        post.header("X-APP-KEY", properties.getAppKey());
        post.body(encryptBody);
        log.info("{}请求报文：{}", methodTag, post);
        HttpResponse httpResponse = post.execute();
        log.info("{}响应报文：{}", methodTag, httpResponse.body());
        JSONObject respJson = JSON.parseObject(httpResponse.body());
        String code = respJson.getString("code");
        if (!Objects.equals("0", code)) {
            log.info("{}状态码失败：{}", methodTag, code);
            return null;
        }
        String biz = respJson.getString("biz");
        String decryptBody = UopRSAUtils.decryptByPrivateKey(biz, properties.getRasPrivateKey());
        JSONObject linkResp = JSON.parseObject(decryptBody);
        log.info("{}响应解密后：{}", methodTag, linkResp);
        if (linkResp != null) {
            return linkResp.getString("link");
        } else {
            return null;
        }
    }

    /**
     * @param link getLink返回的网址
     * @return 返回重定向后的地址
     */
    private String doAutoRedirect(String link, Set<HttpCookie> cookies) {
        final String methodTag = TAG + "-[请求订购界面]";
        HttpRequest get = HttpUtil.createGet(link, false);
        log.info("{}：请求报文{}", methodTag, get);
        HttpResponse firstPageResp = get.execute();
        log.info("{}：响应报文{}", methodTag, firstPageResp);
        // 除了302是重定向还有304
        if (firstPageResp.getStatus() < 300 || firstPageResp.getStatus() >= 400) {
            log.error("{}：获取重定向地址失败", methodTag);
            return null;
        }
        String location = "https://uop.hi.189.cn" + firstPageResp.header("Location");
        HttpRequest redirectReq = HttpUtil.createGet(location);
        HttpResponse execute = redirectReq.execute();
        cookies.addAll(execute.getCookies());
        if (execute.isOk()) {
            return location;
        }
        log.error("{}：获取重定向地址失败", methodTag);
        return null;
    }

    /**
     * 模拟js获取验证码
     */
    private boolean jsGetSmsCode(String location, String prodNbr, Set<HttpCookie> cookies) {
        String getSmsUrl = "https://uop.hi.189.cn/uop-web/haiNanOrderOptionalController/sendSmsByPlatUserDTO";
        HttpRequest smsReq = HttpRequest.post(getSmsUrl);
        Map<String, Object> smsFormMap = new HashMap<>();
        smsFormMap.put("touchNbr", properties.getAppCode());
        smsFormMap.put("prodNo", prodNbr);
        smsReq.form(smsFormMap);
        smsReq.contentType("application/x-www-form-urlencoded");
        smsReq.header("Referer", location);
        smsReq.cookie(cookies);

        log.info("{}-[获取短信验证码]请求报文：{}", TAG, smsReq);
        HttpResponse smsResp = smsReq.execute();
        log.info("{}-[获取短信验证码]响应报文：{}", TAG, smsResp.body());
        if (!smsResp.isOk()) {
            log.error("{}：-[获取短信验证码]", TAG);
            return false;
        }
        // 缓存cookie
        cookies.addAll(smsResp.getCookies());
        JSONObject smsRespJson = JSON.parseObject(smsResp.body());
        String result = smsRespJson.getString("result");
        if (!Objects.equals("TRUE", result)) {
            log.error("{}：获取验证码失败", TAG);
            return false;
        }
        return true;
    }

    /**
     * 查询产品描述并且请求响应里面的图片 模拟的js请求
     *
     * @param prodNbr 产品编码
     */
    private boolean queryProdDescAndReqImg(String location, String prodNbr, Set<HttpCookie> cookies) {
        final String url = "https://uop.hi.189.cn/uop-web/haiNanOrderOptionalController/qryProdDesc";
        HttpRequest formReq = HttpRequest.post(url);
        formReq.form("prodNbr", prodNbr);

        formReq.contentType("application/x-www-form-urlencoded");
        formReq.header("Referer", location);
        formReq.cookie(cookies);

        log.info("{}-[查询产品描述]：{}", TAG, formReq);
        HttpResponse execute = formReq.execute();
        cookies.addAll(execute.getCookies());
        String body = execute.body();
        log.info("{}-[查询产品描述]：{}", TAG, body);
        if (!execute.isOk()) {
            log.error("{}：-[查询产品描述]", TAG);
            return false;
        }
        JSONObject prodResp = JSON.parseObject(body);
        String result = prodResp.getString("result");
        if (!Objects.equals("TRUE", result)) {
            log.error("{}：-[查询产品描述]状态非TRUE", TAG);
            return false;
        }
        //<p><img src=\"https://uop.hi.189.cn/uop-static-web/privilege/html/images/ueditor/20250207/2e5e12e9572e4893a10ceddefd1e3596.png\" title=\"2e5e12e9572e4893a10ceddefd1e3596.png\" alt=\"25元8G.png\"/></p>
        String imgProductsectionHtml = prodResp.getJSONObject("data").getString("imgProductsection");
        Pattern pattern = Pattern.compile("src=\"(.*?)\"");
        Matcher matcher = pattern.matcher(imgProductsectionHtml);
        List<String> imgUrls = new ArrayList<>();
        while (matcher.find()) {
            imgUrls.add(matcher.group(1));
        }
        String imgUrl = imgUrls.get(0);
        HttpRequest imgReq = HttpRequest.get(imgUrl);
        imgReq.header("Referer", location);
        imgReq.cookie(cookies);
        log.info("{}-[请求图片]：{}", TAG, imgReq);
        HttpResponse imgResp = imgReq.execute();
        cookies.addAll(imgResp.getCookies());
        log.info("{}-[请求图片]是否成功：{}", TAG, imgResp.isOk());
        if (!imgResp.isOk()) {
            log.error("{}：-[请求图片]失败", TAG);
            return false;
        }
        return true;
    }

    /**
     * 验证验证码
     *
     * @param smsCode 短信验证码
     *                accNbr 手机号码
     */
    public BenefitPayResultBO verifySmsCode(String accNbr, String smsCode, HNDxPayPkgEnum pagEnum, Map<String, String> cookieMap) {
        HaiNanDxProperties.ProdConfig prodConfig = properties.getProdMap().get(pagEnum);
        final String methodTag = TAG + "-[验证验证码]";
        JSONObject reqJson = new JSONObject();
        reqJson.put("accNbr", accNbr);
        reqJson.put("prodNbr", prodConfig.getProdNbr());
        reqJson.put("code", prodConfig.getCode());
        reqJson.put("sms", smsCode);
        reqJson.put("attrValue", "middle_page");
        final String verifySmsCodeUrl = "https://uop.hi.189.cn/uop-web/haiNanOrderOptionalController/saveOrderBtnDiy";
        HttpRequest postReq = HttpRequest.post(verifySmsCodeUrl);
        postReq.body(reqJson.toJSONString());
        // postReq.header("Referer", "https://uop.hi.189.cn/uop-web/html/order/equityPackage/html/orderPackage_diy.html?code=20240420105317945&&appCode=HNWAIST&authCode=agentSecConfirmPage&prodNbr=20240904091557454");
        postReq.header("X-Requested-With", "XMLHttpRequest");
        postReq.contentType("application/json");
        postReq.header("Accept", "application/json, text/javascript, */*; q=0.01");
        postReq.header("Origin", "https://uop.hi.189.cn");
        postReq.header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        StringBuilder cookieStr = new StringBuilder("SESSION=" + cookieMap.get("SESSION"));
        cookieMap.remove("SESSION");
        for (String key : cookieMap.keySet()) {
            cookieStr.append("; ").append(key).append("=").append(cookieMap.get(key));
        }
        postReq.header("Cookie", cookieStr.toString());

        log.info("{}请求参数{}", methodTag, postReq);
        HttpResponse execute = postReq.execute();
        log.info("{}响应报文{}", methodTag, execute);
        JSONObject respJson = JSON.parseObject(execute.body());
        String resultMsg = respJson.getString("resultMsg");
        if (!Objects.equals("TRUE", respJson.getString("result"))) {
            log.info("{}验证码验证失败:{}", methodTag, resultMsg);
            return new BenefitPayResultBO()
                    .setSuccess(false)
                    .setMessage(resultMsg);
        }
        return new BenefitPayResultBO()
                .setSuccess(true);
    }

    /**
     * 生成请求头X-Auth
     * 请求中必须带有 x-auth 头，格式为： x-auth: appCode;abilityCode;transactionId;sign;reqTime
     * reqTime = 请求发起时间，格式：yyyyMMDDHH24MISS。
     * transactionId=生成规则：appCode+ abilityCode+ 年月日时分秒（YYMMDDHHMMSS，12位） + GUID(32位)，样例： FJDXGONGZHP01001010119041716501286B6FC9C3C777FBEE053AA1410AC76AF。
     */
    private String genHeadAuth(String reqJson, String abilityCode, String transactionId) {
        return properties.getAppCode() + ";" +
                abilityCode + ";" +
                transactionId + ";" +
                genSign(reqJson, abilityCode, transactionId) + ";" +
                LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN);
    }

    /**
     * transactionId=生成规则：appCode+ abilityCode+ 年月日时分秒（YYMMDDHHMMSS，12位） + GUID(32位)，样例： FJDXGONGZHP01001010119041716501286B6FC9C3C777FBEE053AA1410AC76AF。
     */
    private String genTransactionId(String abilityCode) {
        return properties.getAppCode() + abilityCode +
                LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN)
                + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 解密回调加密参数
     */
    public PayResultData decryptNotifyParams(String params) {
        String decryptParams = UopRSAUtils.decryptByPrivateKey(params, properties.getRasPrivateKey());
        JSONObject jsonObject = JSON.parseObject(decryptParams);
        log.info("解密回调参数：{}", jsonObject);
        if (jsonObject != null) {
            return jsonObject.toJavaObject(PayResultData.class);
        }
        log.error("解密回调参数失败");
        return null;
    }

    @Data
    public static class PayResultData {
        /**
         * 订单号
         */
        private String orderCode;

        /**
         * 受理时间(yyyy-MM-dd HH:mm:ss)
         */
        private String acceptTime;

        /**
         * 发展工号
         */
        private String devStaffCode;
        /**
         * 发展手机号
         */
        private String devMobile;
        /**
         * 受理号码
         */
        private String accNbr;
        /**
         * 订购商品编码
         */
        private String prodNbr;
        /**
         * 订购场景编码
         */
        private String code;
        /**
         * 订单状态
         * 1000 创建成功
         * 2000 订单受理成功
         * 3000 订单受理失败
         */
        private String status;
        /**
         * 失败原因
         */
        private String statusReason;
        /**
         * 物流单号
         */
        private String logisticNbr;
        /**
         * 物流状态（见附录三）
         */
        private String logisticStatus;
        /**
         * 联系人姓名
         */
        private String contactName;
        /**
         * 联系人号码
         */
        private String contactNbr;
        /**
         * 客户身份证
         */
        private String custCertNo;
        /**
         * 客户姓名
         */
        private String custName;
        /**
         * iccid 卡号
         */
        private String iccid;
        /**
         * 外部单号
         */
        private String spOpenNo;


        public boolean orderCreateSuccess() {
            return Objects.equals("1000", status);
        }

        public boolean isPaySuccess() {
            return Objects.equals("2000", status);
        }

        public boolean isPayFail() {
            return Objects.equals("3000", status);
        }


    }


    /**
     * 字段名称	父节点	类型	必填	备注
     * accNbr	-	String	是	手机号码
     * authCode	-	String	是	页面鉴权（决定获取的页面，二次确认页获取agentSecConfirmPage）
     * code		String	是	场景编码
     * devMobile		String	是	代理商手机号
     * devStaffCode		String	是	代理商工号
     * prodNbr		String	是	产品编码
     * notifyType		String	是	通知类型，默认传0
     */
    @Data
    static class HaiNanGetLinkReq {
        /**
         * 手机号码
         */
        private String accNbr;
        /**
         * 页面鉴权（决定获取的页面，二次确认页获取agentSecConfirmPage）
         */
        private String authCode;
        /**
         * 场景编码
         */
        private String code;
        /**
         * 代理商手机号
         */
        private String devMobile;

        /**
         * 代理商工号
         */
        private String devStaffCode;
        /**
         * 产品编码
         */
        private String prodNbr;
        /**
         * 通知类型，默认传0
         */
        private String notifyType;

        /**
         * 外部单号
         */
        private String spOpenNo;
    }

    /**
     * 生成签名
     * 小写（MD5（appCode+abilityCode+transactionId+平台分配的密钥+Request Body））,注：Request Body为原始请求报文
     */
    private String genSign(String reqJson, String abilityCode, String transactionId) {
        return SecureUtil.md5(properties.getAppCode() + abilityCode +
                transactionId + properties.getMd5Secret() + reqJson);
    }

    @Data
    @Accessors(chain = true)
    public static class SmsCookieResult {

        private String orderNo;

        private Map<String, String> cookies;

        private List<HttpCookie> cookiesList;

        private boolean success;
    }

}