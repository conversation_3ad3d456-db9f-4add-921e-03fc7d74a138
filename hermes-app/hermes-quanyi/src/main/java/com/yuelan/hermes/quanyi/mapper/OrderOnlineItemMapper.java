package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.OrderOnlineItemDO;
import com.yuelan.hermes.quanyi.controller.request.OrderVirtualListReq;
import com.yuelan.hermes.quanyi.controller.response.OrderVirtualExportRsp;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface OrderOnlineItemMapper extends BaseMapper<OrderOnlineItemDO> {
    int batchInsert(@Param("list") List<OrderOnlineItemDO> list);

    List<String> findByOrderVirtualListReq(@Param("req") OrderVirtualListReq req);

    Long countByOrderVirtualListReq(@Param("req") OrderVirtualListReq req);

    List<Long> cursorByOrderVirtualListReq(@Param("req") OrderVirtualListReq req, @Param("maxId") Long maxId);

    List<OrderVirtualExportRsp> findByOrderIdIn(@Param("orderIds") Collection<Long> orderIds);

    List<OrderOnlineItemDO> findByOrderNo(String orderNo);

    OrderOnlineItemDO findOne(String itemNo);

    int updateStatus(@Param("itemNo") String itemNo, @Param("tarStatus") int status);

    int updateOrderOnlineItemDO(OrderOnlineItemDO orderOnlineItemDO);

    /**
     * 更新供应商订单号
     *
     * @param id              订单明细ID
     * @param supplierOrderNo 供应商订单号
     */
    int updateSupplierOrderNo(@Param("id") Long id, @Param("supplierOrderNo") String supplierOrderNo);

    List<String> findSupplierDistinct();
}