package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccZopOrderDateTotalBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.hermes.quanyi.controller.request.EccZopOrderTotalReq;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 2024/5/6 下午2:11
 */
public interface EccZopOrderDOMapper extends BaseMapper<EccZopOrderDO> {

    default Integer countByPhoneNum(String phoneNum) {
        return selectCount(Wrappers.<EccZopOrderDO>lambdaQuery().eq(EccZopOrderDO::getPhone, phoneNum)).intValue();
    }

    default List<EccZopOrderDO> listExistOrderBySyncOrderNo(List<String> syncOrderNos) {
        return new LambdaQueryChainWrapper<>(EccZopOrderDO.class)
                .in(EccZopOrderDO::getSyncOrderNo, syncOrderNos)
                .list();
    }

    default void updateStatusAndFirstRechargeBySyncOrderNo(String order, String state, Integer firstRecharge, String newPhone, String newRemark) {
        update(Wrappers.<EccZopOrderDO>lambdaUpdate().eq(EccZopOrderDO::getSyncOrderNo, order)
                .set(EccZopOrderDO::getZopOrderState, state)
                .set(firstRecharge != null, EccZopOrderDO::getFirstRecharge, firstRecharge)
                .set(newPhone != null, EccZopOrderDO::getPhone, newPhone)
                .set(newRemark != null, EccZopOrderDO::getRemark, newRemark)
        );
    }

    default EccZopOrderDO getBySyncOrderNo(String syncOrderNo) {
        return selectOne(Wrappers.<EccZopOrderDO>lambdaQuery()
                .eq(EccZopOrderDO::getSyncOrderNo, syncOrderNo));
    }

    default int getOuterChannelOrderCount(Long outChannelId, Integer channelType, String channelOrderNo) {
        return selectCount(Wrappers.<EccZopOrderDO>lambdaQuery()
                .eq(EccZopOrderDO::getChannelId, outChannelId)
                .eq(EccZopOrderDO::getChannelType, channelType)
                .eq(EccZopOrderDO::getChannelOrderNo, channelOrderNo)).intValue();
    }

    default EccZopOrderDO getOuterChannelOrder(Long outChannelId, Integer channelType, String channelOrderNo) {
        return selectOne(Wrappers.<EccZopOrderDO>lambdaQuery()
                .eq(EccZopOrderDO::getChannelId, outChannelId)
                .eq(EccZopOrderDO::getChannelType, channelType)
                .eq(EccZopOrderDO::getChannelOrderNo, channelOrderNo));
    }

    default EccZopOrderDO selectLastZopOrder(String phone, int orderSyncStatus) {
        return selectOne(Wrappers.<EccZopOrderDO>lambdaQuery()
                .eq(EccZopOrderDO::getPhone, phone)
                .eq(EccZopOrderDO::getOrderSyncStatus, orderSyncStatus)
                .orderByDesc(EccZopOrderDO::getCreateTime)
                .last("limit 1"));
    }



    Long orderDateTotalCount(@Param("req") EccZopOrderTotalReq req, @Param("channelLimit") List<Long> channelLimit);

    List<EccZopOrderDateTotalBO> orderDateTotal(@Param("req") EccZopOrderTotalReq req, @Param("channelLimit") List<Long> channelLimit);

    EccZopOrderDateTotalBO orderTotal(@Param("req") EccZopOrderTotalReq req, @Param("channelLimit") List<Long> channelLimit);


    List<EccZopOrderDO> listInnerChannelOrderByFailReason(@Param("keywordsReg") String keywordsReg, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    List<EccZopOrderDO> listExistOrderByChosePhoneAndContactPhone(@Param("orderSource") int orderSource, @Param("list") List<EccZopOrderDO> zopOrders);
}