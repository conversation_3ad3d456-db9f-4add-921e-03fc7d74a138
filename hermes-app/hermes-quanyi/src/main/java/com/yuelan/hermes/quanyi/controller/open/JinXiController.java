package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.service.JinXiService;
import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiCallBackReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 今溪回调接口
 * 回调地址在今溪后台配置
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Slf4j
@Tag(name = "今溪 API")
@RequestMapping("/jinxi")
@RestController
@SaIgnore
public class JinXiController {

    @Autowired
    private JinXiService jinXiService;

    /**
     * 异步结果通知回调
     * 直接用对象接收form-data格式的回调请求
     *
     * @param req 回调请求对象
     * @return 固定返回 success
     */
    @Operation(summary = "异步结果通知回调")
    @RequestMapping(value = "/notify", method = {RequestMethod.GET, RequestMethod.POST})
    public String notify(JinXiCallBackReq req) {
        log.info("今溪异步通知回调请求: {}", JSON.toJSONString(req));

        try {
            // 处理回调
            boolean result = jinXiService.handleNotify(req);
            if (result) {
                log.info("今溪异步通知处理成功: {}", req.getCustomOrderId());
            } else {
                log.error("今溪异步通知处理失败: {}", req.getCustomOrderId());
            }
            return "true";
        } catch (Exception e) {
            log.error("今溪异步通知处理异常", e);
        }
        return "false";

    }
}
