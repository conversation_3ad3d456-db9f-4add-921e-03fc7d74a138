package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.MiGuProxy2PayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.properties.MiGuProxy2Properties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2025/6/9
 * @since 2025/6/9
 * <p>
 * 文档：全国代理版本2/接入文档模版.docx
 */
@Slf4j
@Service
@AllArgsConstructor
public class MiGuProxy2Manager {

    private static final String SMS_REQ_URL = "http://47.100.131.180:8080/xcode/biz/request";
    private static final String SMS_VERIFY_URL = "http://47.100.131.180:8080/xcode/biz/submit";

    private final MiGuProxy2Properties properties;
    private final BenefitOrderLogService benefitOrderLogService;

    /**
     * 发送短信验证码
     *
     * @param phone   手机号
     * @param pkgEnum 枚举包
     * @return 是否成功
     *
     * <p>
     * //参数名	必选	类型	说明
     * // phone	是	String	手机号
     * // productId	是	String	产品编号
     * // outTradeNo	是	String	订单号
     * // channelId	是	String	渠道号
     * // clientIp	否	String	IP地址
     * // utmSource	否	String	推广来源（包名）
     * // utmMedium	否	String	推广方式（媒体）
     * // userAgent	否	String	浏览器UserAgent
     * </p>
     */
    public CommResp sendSmsCode(String phone, String orderNo, MiGuProxy2PayPkgEnum pkgEnum) {
        MiGuProxy2Properties.ProdConfig prodConfig = properties.getProdConfigMap().get(pkgEnum);
        String productId = prodConfig.getProductId();

        String fullUrl = SMS_REQ_URL
                + "?phone=" + phone
                + "&productId=" + productId
                + "&outTradeNo=" + IdUtil.fastSimpleUUID()
                + "&channelId=" + properties.getChannelId();
        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, phone, orderNo);
        HttpRequestWrapper requestWrapper = HttpRequestWrapper.get(fullUrl)
                .log(BenefitPayChannelEnum.MG_QG_PROXY2, args);

        HttpResponse resp = benefitOrderLogService.http(requestWrapper);
        return JSON.parseObject(resp.body(), CommResp.class);
    }


    /**
     * 验证短信验证码
     *
     * @param phone   手机号
     * @param smsCode 短信验证码
     */

    public CommResp verifySmsCode(String phone, String smsCode, String linkId) {
        // http://47.100.131.180:8080/xcode/biz/submit?linkId=1433307022261153794&smsCode=642941
        String fullUrl = SMS_VERIFY_URL
                + "?linkId=" + linkId
                + "&smsCode=" + smsCode;

        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_CHECK, phone);

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.get(fullUrl)
                .log(BenefitPayChannelEnum.MG_QG_PROXY2, args);

        HttpResponse resp = benefitOrderLogService.http(requestWrapper);
        return JSON.parseObject(resp.body(), CommResp.class);
    }

    /**
     * {"code":"00000","data":{"linkId":"1930441984847855618"},"message":"success"}
     */
    @Data
    public static class CommResp {
        private String code;
        private RespData data;
        private String message;

        public boolean isSuccess() {
            return "00000".equals(code);
        }

        @Data
        public static class RespData {
            private String linkId;
        }
    }


}
