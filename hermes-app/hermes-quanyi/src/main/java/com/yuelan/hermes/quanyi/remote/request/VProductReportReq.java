package com.yuelan.hermes.quanyi.remote.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p> 特祯 -订单状态查询</p>
 *
 * <AUTHOR>
 * @date 2024/2/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VProductReportReq extends TezhenBaseReq {
    /**
     * 平台订单号
     */
    @JSONField(name = "OrderId")
    private String orderId;
    /**
     * 代理订单号
     * 代理侧提供的唯一订单号,长度：【1~64字符】
     */
    @JSONField(name = "LinkId")
    private String linkId;


}