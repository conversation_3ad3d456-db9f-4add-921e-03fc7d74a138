package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;

@Data
public class SxfBaseReq<T> {
    /**
     * 天阙平台机构编号
     */
    private String orgId;
    /**
     * 合作方系统生成的唯一请求ID，最大长度32位
     */
    private String reqId;
    /**
     * 每个接口的业务参数
     */
    private T reqData;
    /**
     * 请求时间戳，格式：yyyyMMddHHmmss
     */
    private String timestamp;
    /**
     * 接口版本号，默认值：1.0
     */
    private String version = "1.0";
    /**
     * 签名类型，默认值：RSA
     */
    private String signType = "RSA";
    /**
     * 签名字符串
     */
    private String sign;
}
