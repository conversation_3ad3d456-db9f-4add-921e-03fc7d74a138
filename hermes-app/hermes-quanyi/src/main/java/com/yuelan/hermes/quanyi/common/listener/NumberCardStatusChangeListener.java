package com.yuelan.hermes.quanyi.common.listener;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.manager.AdManager;
import com.yuelan.hermes.quanyi.biz.service.AdChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.EccOuterChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.HttpAsyncTaskService;
import com.yuelan.hermes.quanyi.common.enums.*;
import com.yuelan.hermes.quanyi.common.event.NumberCardStatusChangeEvent;
import com.yuelan.hermes.quanyi.common.pojo.bo.AdExtraBo;
import com.yuelan.hermes.quanyi.common.pojo.domain.AdChannelDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.config.task.HttpTaskRequest;
import com.yuelan.hermes.quanyi.remote.AdAgentPlatformManager;
import com.yuelan.hermes.quanyi.remote.request.OrderChangeStatusReq;
import com.yuelan.hermes.quanyi.remote.request.OrderSyncReq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> 2024/12/28
 * @since 2024/12/28
 */
@Slf4j
@Component
@AllArgsConstructor
public class NumberCardStatusChangeListener {

    public static final String BUSINESS_TYPE = "ECC_NC_API_CALLBACK";

    private final AdChannelDOService adChannelDOService;
    private final AdManager adManager;
    private final AdAgentPlatformManager adAgentPlatformManager;
    private final HttpAsyncTaskService httpAsyncTaskService;
    private final EccOuterChannelDOService eccOuterChannelDOService;

    /**
     * 广告回传 & 渠道回传
     * 广告回传：直接或者通过投放平台回传给广告投放平台
     * 渠道回传：回传给分销渠道
     *
     * @param event 事件
     */
    @EventListener(NumberCardStatusChangeEvent.class)
    public void handleNumberCardStatusChangeEvent(NumberCardStatusChangeEvent event) {
        log.info("NumberCardStatusChangeEvent:{}", JSONObject.toJSONString(event));
        // 处理广告回传
        ThreadUtil.execute(() -> handlerAdCallBack(event));
        // 渠道回传
        ThreadUtil.execute(() -> handlerChannelCallBack(event));
    }

    /**
     * 渠道回传
     */
    private void handlerChannelCallBack(NumberCardStatusChangeEvent event) {
        EccNcOrderDO eccNcOrderDO = event.getEccNcOrderDO();
        if (StrUtil.isEmpty(eccNcOrderDO.getCallbackUrl())) {
            return;
        }
        HttpTaskRequest request = order2Request(eccNcOrderDO);
        httpAsyncTaskService.createTask(request);
    }


    /**
     * 处理广告回传
     */
    private void handlerAdCallBack(NumberCardStatusChangeEvent event) {
        EccNcOrderDO eccNcOrderDO = event.getEccNcOrderDO();
        if (Objects.nonNull(eccNcOrderDO.getAdAgentPlatform())) {
            // 通过投放平台回传
            adAgentPlatformAdCallBack(event);
        } else {
            // 自己回传
            thisPlatformAdCallBack(event);
        }
    }

    private void adAgentPlatformAdCallBack(NumberCardStatusChangeEvent event) {
        NcOrderStatusEnum orderStatus = event.getOrderStatus();
        SimCardStatusEnum simCardStatus = event.getSimCardStatus();
        Integer rechargeAmount = event.getRechargeAmount();
        EccNcOrderDO eccNcOrderDO = event.getEccNcOrderDO();

        if (NcOrderStatusEnum.GET_CARD_SUCCESS.equals(orderStatus) || NcOrderStatusEnum.GET_CARD_FAIL.equals(orderStatus) || NcOrderStatusEnum.SUBMIT_FAIL.equals(orderStatus)) {
            userGetCardSuccessOrFailSync(eccNcOrderDO);
        }

        if (SimCardStatusEnum.ACTIVE.equals(simCardStatus)) {
            if (rechargeAmount != null) {
                orderStatusChangeSync(eccNcOrderDO, OrderChangeStatusEnum.ACTIVE_FIRST_RECHARGE);
            } else {
                orderStatusChangeSync(eccNcOrderDO, OrderChangeStatusEnum.ACTIVE);
            }
        } else if (rechargeAmount != null) {
            orderStatusChangeSync(eccNcOrderDO, OrderChangeStatusEnum.FIRST_RECHARGE);
        } else if (NcOrderStatusEnum.REFUND.equals(orderStatus)) {
            orderStatusChangeSync(eccNcOrderDO, OrderChangeStatusEnum.REFUND);
        }

    }

    /**
     * 通过自身平台回传广告
     *
     * @param event 事件对象
     */
    private void thisPlatformAdCallBack(NumberCardStatusChangeEvent event) {
        NcOrderStatusEnum orderStatus = event.getOrderStatus();
        Integer rechargeAmount = event.getRechargeAmount();


        if (NcOrderStatusEnum.GET_CARD_SUCCESS.equals(orderStatus)) {
            adCallback(event.getEccNcOrderDO(), EccTrackingEventEnum.GET_CARD_SUCCESS);
        }
        if (rechargeAmount != null) {
            adCallback(event.getEccNcOrderDO(), EccTrackingEventEnum.FIRST_RECHARGE);
        }
    }

    /**
     * 明确的用户领卡成功或者失败同步，因为最开始插入数据库只是一个提交成功，或者提交失败的状态。不是最终的成功或者失败的状态
     *
     * @param orderDO 订单
     */
    private void userGetCardSuccessOrFailSync(EccNcOrderDO orderDO) {
        try {
            OrderSyncReq orderSyncReq = buildOrderSyncReq(orderDO);
            if (Objects.isNull(orderSyncReq)) {
                return;
            }
            Long adChannelId = orderDO.getAdChannelId();
            if (Objects.isNull(adChannelId)) {
                return;
            }
            AdChannelDO adChannelDO = adChannelDOService.getByModuleAndAdChannelId(SysModuleEnum.ECC, adChannelId);
            if (Objects.isNull(adChannelDO)) {
                return;
            }
            AdExtraBo adExtraBo = buildAdExtraBo(orderDO, adChannelDO);
            orderSyncReq.setAdExtra(JSON.parseObject(JSON.toJSONString(adExtraBo)));
            adAgentPlatformManager.userOrderCreateNotify(orderSyncReq);
        } catch (Exception e) {
            log.error("用户领卡成功或者失败同步失败", e);
        }
    }

    /**
     * 订单状态变更同步
     *
     * @param orderDO               订单对象
     * @param orderChangeStatusEnum 订单状态枚举
     */
    private void orderStatusChangeSync(EccNcOrderDO orderDO, OrderChangeStatusEnum orderChangeStatusEnum) {
        try {
            OrderChangeStatusReq req = new OrderChangeStatusReq();
            req.setChannelOrderNo(orderDO.getOrderNo());
            req.setStatus(orderChangeStatusEnum.getCode());
            if (OrderChangeStatusEnum.FIRST_RECHARGE.equals(orderChangeStatusEnum)) {
                req.setFirstChargeAmount(orderDO.getFirstChargeAmount());
                req.setFirstChargeTime(orderDO.getFirstChargeTime());
            } else if (OrderChangeStatusEnum.ACTIVE.equals(orderChangeStatusEnum)) {
                req.setActiveTime(orderDO.getActivateTime());
            } else if (OrderChangeStatusEnum.ACTIVE_FIRST_RECHARGE.equals(orderChangeStatusEnum)) {
                req.setActiveTime(orderDO.getActivateTime());
                req.setFirstChargeAmount(orderDO.getFirstChargeAmount());
                req.setFirstChargeTime(orderDO.getFirstChargeTime());
            }
            adAgentPlatformManager.orderStatusChangeNotify(req);
        } catch (Exception e) {
            log.error("订单状态变更同步失败", e);
        }
    }


    /**
     * 构建订单同步请求对象
     *
     * @param orderDO 订单对象
     * @return 订单同步请求对象
     */
    private OrderSyncReq buildOrderSyncReq(EccNcOrderDO orderDO) {
        OrderSyncReq orderSyncReq = new OrderSyncReq();
        orderSyncReq.setChannelOrderNo(orderDO.getOrderNo());
        orderSyncReq.setProvince(orderDO.getProvince());
        orderSyncReq.setCity(orderDO.getCity());
        orderSyncReq.setPhone(orderDO.getPhone());
        orderSyncReq.setProductId(orderDO.getProdId());
        orderSyncReq.setOrderTime(orderDO.getCreateTime());
        NcOrderStatusEnum ncOrderStatusEnum = NcOrderStatusEnum.of(orderDO.getOrderStatus());
        if (NcOrderStatusEnum.GET_CARD_SUCCESS.equals(ncOrderStatusEnum)) {
            orderSyncReq.setSuccess(1);
        } else if (NcOrderStatusEnum.GET_CARD_FAIL.equals(ncOrderStatusEnum) || NcOrderStatusEnum.SUBMIT_FAIL.equals(ncOrderStatusEnum)) {
            orderSyncReq.setSuccess(0);
        } else {
            return null;
        }
        orderSyncReq.setFailReason(orderDO.getFailReason());
        return orderSyncReq;
    }

    /**
     * 构建广告额外信息对象
     *
     * @param orderDO     订单对象
     * @param adChannelDO 广告渠道对象
     * @return 广告额外信息对象
     */
    private AdExtraBo buildAdExtraBo(EccNcOrderDO orderDO, AdChannelDO adChannelDO) {
        AdExtraBo adExtraBo = new AdExtraBo();
        AdChannelCodeEnum adChannelEnum = AdChannelCodeEnum.of(adChannelDO.getAdChannelCode());
        if (AdChannelCodeEnum.DOU_YIN_APPLET == adChannelEnum) {
            AdExtraBo.TiktokAppletParam param = new AdExtraBo.TiktokAppletParam();
            param.setTiktokInitParams(orderDO.getPageUrl());
            if (Objects.nonNull(orderDO.getAdExt())) {
                param.setExtra(JSONUtil.parseObj(orderDO.getAdExt()));
            }
            adExtraBo.setTiktokParam(param);
        } else if (AdChannelCodeEnum.KUAI_SHOU == adChannelEnum) {
            AdExtraBo.KsH5Param param = new AdExtraBo.KsH5Param();
            param.setKsH5Url(orderDO.getPageUrl());

            if (Objects.nonNull(orderDO.getAdExt())) {
                param.setExtra(JSONUtil.parseObj(orderDO.getAdExt()));
            }
            adExtraBo.setKsH5Param(param);
        } else if (AdChannelCodeEnum.TENCENT_H5 == adChannelEnum) {
            AdExtraBo.TencentH5Param param = new AdExtraBo.TencentH5Param();
            param.setTencentH5Url(orderDO.getPageUrl());
            if (Objects.nonNull(orderDO.getAdExt())) {
                param.setExtra(JSONUtil.parseObj(orderDO.getAdExt()));
            }
            adExtraBo.setTencentH5Param(param);
        }
        return adExtraBo;
    }


    /**
     * 广告回调-直接对媒体平台进行回调
     *
     * @param orderDO           订单
     * @param trackingEventEnum 回调事件
     */
    private void adCallback(EccNcOrderDO orderDO, EccTrackingEventEnum trackingEventEnum) {
        try {
            Long adChannelId = orderDO.getAdChannelId();
            if (Objects.isNull(adChannelId)) {
                return;
            }
            JSONObject adExt = JSONObject.parseObject(orderDO.getAdExt());
            AdChannelDO adChannelDO = adChannelDOService.getByModuleAndAdChannelId(SysModuleEnum.ECC, adChannelId);
            AdChannelCodeEnum adChannelCodeEnum = AdChannelCodeEnum.of(adChannelDO.getAdChannelCode());
            adManager.adCallBackHandlerCall(adChannelCodeEnum, adExt, trackingEventEnum, Boolean.FALSE);
        } catch (Exception e) {
            log.error("广告直接回调失败", e);
        }
    }


    private HttpTaskRequest order2Request(EccNcOrderDO eccNcOrderDO) {
        return HttpTaskRequest.builder()
                .url(eccNcOrderDO.getCallbackUrl())
                .method("POST")
                .body(eccOuterChannelDOService.buildSignBody(eccNcOrderDO))
                .headers(null)
                .sourceSystem("ECC_NC_API")
                // 与回调注册的业务类型对应
                .businessType(BUSINESS_TYPE)
                .businessId(eccNcOrderDO.getOrderNo())
                .maxRetryCount(6)
                .retryInterval(300)
                // code == 0 为成功
                .successStrategy(SuccessStrategyEnum.RESPONSE_JSON_CODE_0)
                .build();
    }


}
