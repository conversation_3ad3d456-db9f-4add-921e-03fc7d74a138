package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.controller.request.BenefitOrderListReq;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2024/4/7 19:58
 */
public interface BenefitOrderDOMapper extends MPJBaseMapper<BenefitOrderDO> {

    default BenefitOrderDO selectOneByOrderNo(String orderNo) {
        return selectOne(Wrappers.<BenefitOrderDO>lambdaQuery()
                .eq(BenefitOrderDO::getOrderNo, orderNo));
    }

    long pageCount(@Param(Constants.WRAPPER)Wrapper<BenefitOrderDO> queryWrapper);
    List<BenefitOrderDO> joinAdInfoPage(@Param(Constants.WRAPPER)Wrapper<BenefitOrderDO> queryWrapper,
                                         @Param("limit") long limit,
                                         @Param("offset") long offset);


    default void updateOrderStatusByOrderId(Long orderId, Integer orderStatus) {
        update(Wrappers.<BenefitOrderDO>lambdaUpdate()
                .set(BenefitOrderDO::getOrderStatus, orderStatus)
                .eq(BenefitOrderDO::getOrderId, orderId));
    }

    default BenefitOrderDO getPhoneAndProdOrderByPayStatus(String phone, Long prodId, Integer orderStatus) {
        return selectOne(Wrappers.<BenefitOrderDO>lambdaQuery()
                .eq(BenefitOrderDO::getPhone, phone)
                .eq(BenefitOrderDO::getProdId, prodId)
                .eq(BenefitOrderDO::getPayStatus, orderStatus)
                .orderByDesc(BenefitOrderDO::getOrderId)
                .last("limit 1")
        );
    }

    default List<BenefitOrderDO> selectByPhoneProIdStatus(String phone, Integer channelId, Long prodId, Integer payStatus, Integer limit) {
        return selectList(new LambdaQueryWrapper<BenefitOrderDO>()
                .eq(StringUtils.isNotBlank(phone), BenefitOrderDO::getPhone, phone)
                .eq(channelId != null, BenefitOrderDO::getPayChannelId, channelId)
                .eq(prodId != null, BenefitOrderDO::getProdId, prodId)
                .eq(payStatus != null, BenefitOrderDO::getPayStatus, payStatus)
                .last(limit != null, "limit " + limit));
    }

    default List<BenefitOrderDO> getJSUniconJobOrderList(List<Integer> channelIds, Integer payStatus, Integer limit) {
        return selectList(new LambdaQueryWrapper<BenefitOrderDO>()
                .in(CollectionUtils.isNotEmpty(channelIds), BenefitOrderDO::getPayChannelId, channelIds)
                .eq(payStatus != null, BenefitOrderDO::getPayStatus, payStatus)
                .isNotNull(BenefitOrderDO::getOutOrderNo)
                .last(limit != null, "limit " + limit));
    }

    /**
     * 减少剩余领取次数
     */
    int decreaseOrderRemainNum(@Param("orderId") Long orderId);

    /**
     * 查询订单 根据手机号码和extData
     */
    default BenefitOrderDO selectOneByPhoneAndExtData(String phone, String extData) {
        return selectOne(Wrappers.<BenefitOrderDO>lambdaQuery()
                .eq(BenefitOrderDO::getPhone, phone)
                .eq(BenefitOrderDO::getExtraData, extData));
    }
}