package com.yuelan.hermes.quanyi.remote.request.zop;

import com.chinaunicom.zop.api.model.ZopRequest;
import com.yuelan.hermes.quanyi.remote.response.zop.KingOrderQueryResp;
import lombok.Data;

/**
 * <AUTHOR> 2024/5/8 下午5:41
 * contactNum	N	String	V11	联系电话 (证件或联系电话需传其中一项)
 * certNo	N	String	V18	证件号 (证件或联系电话需传其中一项)
 * timeRange	Y	String	V2	必填项;时间范围;可选参数:7、9、10；单位（天）
 * goodsId	N	String	V12	商品编码;可传省份商品（111609180703），查该商品省份订单；或可传总部商品（981609180703），查该商品所有订单
 * productId	N	String	V8	产品ID
 */
@Data
public class KingOrderQueryReq implements ZopRequest<KingOrderQueryResp> {

    /**
     * 联系电话 (证件或联系电话需传其中一项)
     */
    private String contactNum;
    /**
     * 证件号 (证件或联系电话需传其中一项)
     */
    private String certNo;
    /**
     * 必填项;时间范围;可选参数:7、9、10；单位（天）
     */
    private String timeRange;
    /**
     * 商品编码;可传省份商品（111609180703），查该商品省份订单；或可传总部商品（981609180703），查该商品所有订单
     */
    private String goodsId;
    /**
     * 产品ID
     */
    private String productId;


    @Override
    public String getPath() {
        return "link/king/card/order/search";
    }

    @Override
    public String getPathName() {
        return "订单查询服务";
    }

    @Override
    public Class<KingOrderQueryResp> getResponseClass() {
        return KingOrderQueryResp.class;
    }
}
