package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.SmsTemplateDO;
import com.yuelan.hermes.quanyi.controller.response.SmsTemplateRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SmsTemplateMapper extends BaseMapper<SmsTemplateDO> {

    List<SmsTemplateDO> findByTemplateNo(@Param("templateNo") String templateNo);

    List<SmsTemplateRsp> findSmsTemplateRspByBizType(@Param("bizType") Integer bizType);
}