package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.OrderOnlineDO;
import com.yuelan.hermes.quanyi.controller.request.OrderVirtualListReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderOnlineMapper extends BaseMapper<OrderOnlineDO> {

    OrderOnlineDO findByOrderNo(String orderNo);

    int updateOrderStatus(@Param("orderNo") String orderNo, @Param("status") Integer status, @Param("remark") String remark);

    OrderOnlineDO findByOutOrderNoAndPurchaserType(@Param("outOrderNo") String outOrderNo, @Param("purchaserType") Integer purchaserType);

    int updateNotifyStatus(@Param("orderId") Long orderId, @Param("notifyStatus") Integer notifyStatus);

    Long countByOrderVirtualListReq(@Param("req") OrderVirtualListReq req);

    List<OrderOnlineDO> pageByOrderVirtualListReq(@Param("req") OrderVirtualListReq req);

}