package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.common.pojo.properties.HuBeiShuKeEduProperties;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduOrderConfirmReq;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduSmsCodeReq;
import com.yuelan.hermes.quanyi.remote.hbsk.response.ActivityInfoResp;
import com.yuelan.hermes.quanyi.remote.hbsk.response.EduOrderConfirmResp;
import com.yuelan.hermes.quanyi.remote.hbsk.response.EduSmsCodeResp;
import com.yuelan.hermes.quanyi.remote.hbsk.response.TelecomCheckResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 * <p>
 * 湖北数科-教育包
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HuBeiShuKeEduManager {

    public static final String USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1";
    public static final String DEVICE_INFO = "iPhone; CPU iPhone OS 16_6 like Mac OS X";
    private static final String SEND_SMS_CODE_KEY = "egame@2024";
    private static final String CONFIRM_ORDER_AES_KEY = "239F7F7ECD803D4B";
    private static final String CONFIRM_ORDER_SIGN_APPEND = "wSyspGqUyKcYwNjv";
    private final HuBeiShuKeEduProperties huBeiShuKeEduProperties;

    /**
     * 检查手机号是否为电信用户（返回完整响应对象）
     */
    public TelecomCheckResp checkIsTelecomWithResponse(String mobile) {
        String url = "https://api.play.cn/api/v1/cloud_center/" + mobile + "/isTelecom";

        log.info("检查电信用户请求地址: {}", url);

        HttpResponse response = HttpRequest.get(url)
                .header(Header.ACCEPT, "application/json, text/plain, */*")
                .header(Header.ORIGIN, "https://act.play.cn")
                .header(Header.REFERER, "https://act.play.cn/")
                .header(Header.USER_AGENT, USER_AGENT)
                .execute();

        String body = response.body();
        log.info("检查电信用户返回参数: {}", body);

        return JSON.parseObject(body, TelecomCheckResp.class);
    }

    /**
     * 发送短信验证码
     *
     * @param req 短信验证码请求
     * @return 短信验证码响应
     */
    public EduSmsCodeResp sendSmsCode(EduSmsCodeReq req) {
        String url = "https://api.play.cn/api/v1/sale/product/order/smscode";
        setSendSmsCodeSign(req);
        log.info("教育包发送短信验证码请求地址: {}", url);
        log.info("教育包发送短信验证码请求参数: {}", JSON.toJSONString(req));

        HttpResponse response = HttpRequest.post(url)
                .header(Header.ACCEPT, "application/json, text/plain, */*")
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .header(Header.ORIGIN, "https://act.play.cn")
                .header(Header.REFERER, "https://act.play.cn/")
                .header(Header.USER_AGENT, USER_AGENT)
                .body(JSON.toJSONString(req))
                .execute();

        String body = response.body();
        log.info("教育包发送短信验证码返回参数: {}", body);

        return JSON.parseObject(body, EduSmsCodeResp.class);
    }

    private void setSendSmsCodeSign(EduSmsCodeReq req) {
        String text = req.getSale_product_id() + req.getPhone() + req.getApp_id() + SEND_SMS_CODE_KEY;
        req.setSign(SecureUtil.md5(text).toLowerCase());
    }


    /**
     * 订单确认
     *
     * @param req 订单确认请求
     * @return 订单确认响应
     */
    public EduOrderConfirmResp confirmOrder(EduOrderConfirmReq req) {
        String url = "https://api.play.cn/api/v1/sale/product/orderConfirm";

        req.setSign(confirmOrderSign(req));

        log.info("教育包订单确认请求地址: {}", url);
        log.info("教育包订单确认请求参数: {}", JSON.toJSONString(req));

        HttpResponse response = HttpRequest.post(url)
                .header(Header.ACCEPT, "application/json, text/plain, */*")
                .header(Header.CONTENT_TYPE, "application/json;charset=UTF-8")
                .header(Header.ORIGIN, "https://act.play.cn")
                .header(Header.REFERER, "https://act.play.cn/")
                .header(Header.USER_AGENT, USER_AGENT)
                .body(JSON.toJSONString(req))
                .execute();

        String body = response.body();
        log.info("教育包订单确认返回参数: {}", body);

        return JSON.parseObject(body, EduOrderConfirmResp.class);
    }

    /**
     * 提交验证码-加密手机号
     *
     * @param phone 手机号
     */
    public String confirmOrderEncryptPhone(String phone) {
        // hutool Aes ECB/PKCS5Padding
        AES aes = new AES(Mode.ECB, Padding.PKCS5Padding, CONFIRM_ORDER_AES_KEY.getBytes(StandardCharsets.UTF_8));
        return aes.encryptBase64(phone);
    }

    public String confirmOrderSign(EduOrderConfirmReq req) {
        String text = req.getPhone() + req.getSource_id() + req.getSale_product_id() + req.getCp_channel_code() + req.getTime_stamp() + CONFIRM_ORDER_SIGN_APPEND;
        return SecureUtil.md5(text).toLowerCase();
    }


    /**
     * 获取活动信息
     *
     * @param activityId 活动ID
     * @return 活动信息响应
     */
    public ActivityInfoResp getActivityInfo(String activityId) {
        String url = "https://api.play.cn/api/v1/promotional/activity/" + activityId + "/info";

        log.info("获取活动信息请求地址: {}", url);

        HttpResponse response = HttpRequest.get(url)
                .header(Header.ACCEPT, "application/json, text/plain, */*")
                .header(Header.ORIGIN, "https://act.play.cn")
                .header(Header.REFERER, "https://act.play.cn/")
                .header(Header.USER_AGENT, USER_AGENT)
                .header("User-Agent", USER_AGENT)
                .execute();

        String body = response.body();
        log.info("获取活动信息返回参数: {}", body);

        return JSON.parseObject(body, ActivityInfoResp.class);
    }


}
