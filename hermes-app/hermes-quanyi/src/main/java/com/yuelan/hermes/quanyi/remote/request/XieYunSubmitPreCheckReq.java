package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;

/**
 * <AUTHOR> 2024/12/5
 * @since 2024/12/5
 */
@Data
public class XieYunSubmitPreCheckReq {

    /**
     * 办理号码
     */
    private String phone;

    /**
     * 号码省份编码
     */
    private String provinceCode;

    /**
     * 区域编码 （ 到 市 级）
     */
    private String areaCode;

    /**
     * 用户类型用户类型：默认传 1
     */
    private String userIdType;

    /**
     * 订单备注 非必填
     */
    private String orderRemark;

    /**
     * 订购产品ID
     * 升龙卡 TpFK8IriDimWt0Qt
     * 祥龙卡 XWjhgz5F6me9ZZH9
     * 鑫龙卡 Wg1oPPs26NZMzZjH
     * 腾龙卡 OzEW6cbGUHb8DHfD
     * 锦龙卡 i81x1Sy3w48SP0nS
     * 瑞龙卡 BhQcnyL6SCs8XNsx
     */
    private String offerId;

    /**
     * 用户 ID String 是 生成规则：身份证后 6 位+年月日+6位随机数
     */
    private String userId;

    /**
     * 收货信息
     */
    private OrderRecvAddr orderRecvAddr;

    /**
     * 客户信息
     */
    private CustInfo custInfo;

    @Data
    public static class CustInfo {
        /**
         * 客户姓名
         */
        private String custName;
        /**
         * 客户证件类型
         */
        private String idenType;
        /**
         * 客户证件号码
         */
        private String idenNr;
        /**
         * 客户手机号码
         */
        private String contNumber;

    }

    @Data
    public static class OrderRecvAddr {
        /**
         * 收货人姓名
         */
        private String receiveName;

        /**
         * 所在国家 中国
         */
        private String receiveCountry;

        /**
         * 所在省份代码
         */
        private String receiveProvince;

        /**
         * 所在省份名称
         */
        private String receiveProvinceName;

        /**
         * 所在市代码
         */
        private String receiveCity;

        /**
         * 所在市名称
         */
        private String receiveCityName;

        /**
         * 所在区县代码
         */
        private String receiveCounty;

        /**
         * 所在区县名称
         */
        private String receiveCountyName;

        /**
         * 详细地址
         */
        private String receiveAddress;

        /**
         * 收货人手机号
         */
        private String receiveMobile;
    }

}
