package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductDO;
import com.yuelan.hermes.quanyi.controller.request.GamingProductListReq;
import com.yuelan.hermes.quanyi.controller.response.ProductSearchRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GamingProductMapper extends BaseMapper<GamingProductDO> {
    int batchInsert(@Param("list") List<GamingProductDO> list);

    List<ProductSearchRsp> search(@Param("productId") Long productId, @Param("productCode") String productCode, @Param("productName") String productName);

    GamingProductDO findByProductCode(String productCode);

    GamingProductDO findByProductId(Long productId);

    List<GamingProductDO> pageByGamingProductListReq(@Param("req") GamingProductListReq req);

    Long countByGamingProductListReq(@Param("req") GamingProductListReq req);

    int updateStatus(@Param("productId") Long productId, @Param("status") Integer status);

    int removeByProductId(Long productId);

    List<GamingProductDO> findByMgModel(Integer mgModel);

    List<GamingProductDO> findByProductType(Integer productType);

    List<GamingProductDO> listAll();
}