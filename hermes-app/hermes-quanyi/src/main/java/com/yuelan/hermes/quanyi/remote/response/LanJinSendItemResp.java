package com.yuelan.hermes.quanyi.remote.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 蓝鲸电竞卡 - 发放道具响应
 *
 * <AUTHOR> Generated
 * @since 2025-07-10
 */
@Data
public class LanJinSendItemResp {

    /**
     * 响应码（0：成功，1：失败）
     */
    @Schema(description = "响应码（0：成功，1：失败）")
    private Integer code;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息")
    private String msg;

    /**
     * 判断是否成功
     *
     * @return true：成功，false：失败
     */
    public boolean isSuccess() {
        return Integer.valueOf(0).equals(code);
    }
}
