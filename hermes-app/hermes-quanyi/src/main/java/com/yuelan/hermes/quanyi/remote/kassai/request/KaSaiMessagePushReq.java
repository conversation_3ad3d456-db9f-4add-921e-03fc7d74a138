package com.yuelan.hermes.quanyi.remote.kassai.request;

import lombok.Data;

/**
 * 同步消息请求体
 */
@Data
public class KaSaiMessagePushReq {

    /**
     * 移动订单号
     */
    private String orderId;

    /**
     * 渠道订单号
     */
    private String orderNum;

    /**
     * 订购号码
     */
    private String mobile;

    /**
     * 100,200,201,202,203,300,301,302,303,304,1000,1005,1010,1015,1020,1025,1030,1035,1040,1055,1065
     */
    private Integer status;

    /**
     * 100：初始状态,200：待支付,201：支付中,202：支付成功,203：支付失败,300：待订购,301：订购中,302：订购成功,303：订购失败,304：订购待重试,1000：待商户确认,1005：预处理成功,1010：预处理失败,1015：备货中,1020：已发货,1025：订购完成,1030：订购失败,1035：已激活,1040：激活失败,1055：t月充值,1065：oao充值，其中303、1030、1035是订购终态
     */
    private String statusDesc;

    /**
     * 修改时间 时间戳(毫秒级)
     */
    private String updateTime;

    /**
     * 激活时间 时间戳(毫秒级)
     */
    private String activateTime;

    /**
     * T月充值
     */
    private Integer monthRecharge;

    /**
     * T+1月充值
     */
    private Integer nextMonthRecharge;

    /**
     * 是否是oao充值 0否1是
     */
    private Integer oaoRechargeStatus;

    /**
     * 物流公司
     */
    private String shipmentCompanyName;

    /**
     * 物流订单号
     */
    private String shipmentNumber;

    /**
     * oao充值档位 0 无 1  50元挡 2 100元及以上
     */
    private Integer oaoRechargeGear;

    /**
     * 第三方透传字段
     */
    private String productId;

    /**
     * 第三方透传字段
     */
    private String empId;

    /**
     * 第三方透传字段
     */
    private String flag;

    /**
     * 用户手机号 第三方透传字段
     */
    private String custMobile;

    /**
     * 用户名称 第三方透传字段
     */
    private String custName;
}
