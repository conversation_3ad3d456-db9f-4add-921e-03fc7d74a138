package com.yuelan.hermes.quanyi.remote.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WyOrderCreateReq {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "用户手机号")
    private String phone;

    @Schema(description = "第三方商品编号")
    private String goodsNo;

    @Schema(description = "请求时间(13位时间戳)")
    private Long timestamp;

    @Schema(description = "签名")
    private String sign;

}
