package com.yuelan.hermes.quanyi.remote.hbsk.request;

import lombok.Data;

/**
 * 教育包订单确认请求
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class EduOrderConfirmReq {

    /**
     * 手机号（加密）
     */
    private String phone;

    /**
     * 应用ID = 配置文件里面appId
     */
    private String app_id;

    /**
     * 来源ID = ActivityDetail.activityId
     */
    private Integer source_id;

    /**
     * 来源名称 =ActivityDetail.activityName
     */
    private String source_name;

    /**
     * 销售产品ID =ActivityDetail.ActivityGood.saleProductId
     */
    private String sale_product_id;

    /**
     * 用户代理 =ua
     * Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
     */
    private String ua;

    /**
     * 设备信息 =估计是 UA里面截取处理啊的 iPhone; CPU iPhone OS 16_6 like Mac OS X
     */
    private String device_info;

    /**
     * 短信验证码 6位验证码
     */
    private String sms_code;

    /**
     * 时间戳 毫秒
     */
    private Long time_stamp;

    /**
     * CP渠道代码 = 链接上的cp_channel_code
     */
    private String cp_channel_code;

    /**
     * 签名
     */
    private String sign;

    /**
     * 应用名称=空字符串
     */
    private String app_name;

    /**
     * 省份支付类型 =空字符串  本来是 js 从链接上获取的 现在是拿不到
     */
    private String province_pay_type;
}
