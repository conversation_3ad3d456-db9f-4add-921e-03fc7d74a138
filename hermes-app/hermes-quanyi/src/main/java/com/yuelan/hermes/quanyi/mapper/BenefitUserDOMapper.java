package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitUserDO;

/**
 * <AUTHOR> 2024/4/10 下午2:37
 */
public interface BenefitUserDOMapper extends BaseMapper<BenefitUserDO> {

    default BenefitUserDO selectOneByPhone(String phone) {
        return selectOne(Wrappers.<BenefitUserDO>lambdaQuery().eq(BenefitUserDO::getPhone, phone));
    }

}