package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO;
import com.yuelan.hermes.quanyi.controller.request.GamingOrderListReq;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

public interface GamingOrderMapper extends BaseMapper<GamingOrderDO> {
    int batchInsert(@Param("list") List<GamingOrderDO> list);

    GamingOrderDO findByMerchantOrder(@Param("merchantId") Long merchantId, @Param("outOrderNo") String outOrderNo);

    List<GamingOrderDO> pageByGamingOrderListReq(@Param("req") GamingOrderListReq req);

    Long countByGamingOrderListReq(@Param("req") GamingOrderListReq req);

    List<GamingOrderDO> cursorByGamingOrderListReq(@Param("req") GamingOrderListReq req, @Param("maxId") Long maxId);

    GamingOrderDO findByOrderNo(@Param("orderNo") String orderNo);

    GamingOrderDO findByOrderId(Long orderId);

    int updateStatus(@Param("orderId") Long orderId, @Param("status") Integer status);

    int updateNotifyStatus(@Param("orderId") Long orderId, @Param("notifyStatus") Integer notifyStatus);


    default List<GamingOrderDO> selectListByMerchantIdAndNotifyStatus(Long id, Integer statusEnums, Integer limit) {
        return selectList(Wrappers.<GamingOrderDO>lambdaQuery()
                .eq(GamingOrderDO::getMerchantId, id)
                .eq(GamingOrderDO::getNotifyStatus, statusEnums)
                .last(Objects.nonNull(limit), "limit " + limit)
        );
    }

    default Integer getOrderNotifyStatus(Long orderId) {
        return selectOne(Wrappers.<GamingOrderDO>lambdaQuery().select(GamingOrderDO::getNotifyStatus)
                .eq(GamingOrderDO::getOrderId, orderId)).getNotifyStatus();
    }

    default List<GamingOrderDO> listByOrderStatus(Integer orderStatus, Long limit) {
        return selectList(Wrappers.<GamingOrderDO>lambdaQuery()
                .eq(GamingOrderDO::getOrderStatus, orderStatus).last(Objects.nonNull(limit), "limit " + limit));
    }

    default int selectByPhoneCount(String phone){
        return selectCount(Wrappers.<GamingOrderDO>lambdaQuery().eq(GamingOrderDO::getPhone,phone)).intValue();
    }


    /**
     * 查询用户 电竞卡订单数量
     */
    int gamingOrderCountByPhone(@Param("phone") String phone);

    /**
     * 查询用户所有订单
     */
    List<GamingOrderDO> listByPhone(@Param("phone") String phone, @Param("start") LocalDateTime startTime, @Param("end") LocalDateTime endTime);
}