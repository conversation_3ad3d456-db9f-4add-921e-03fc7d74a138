package com.yuelan.hermes.quanyi.remote.gaming;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.pojo.properties.LanJinProperties;
import com.yuelan.hermes.quanyi.remote.request.LanJinSendItemReq;
import com.yuelan.hermes.quanyi.remote.request.LanJinUserInfoReq;
import com.yuelan.hermes.quanyi.remote.response.LanJinSendItemResp;
import com.yuelan.hermes.quanyi.remote.response.LanJinUserInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 蓝鲸电竞卡接口管理服务
 *
 * <AUTHOR> Generated
 * @since 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LanJinManager {

    private static final String LOG_TAG = "[蓝鲸烟雨江湖]";

    private final LanJinProperties lanJinProperties;


    /**
     * 查看用户信息
     *
     * @param account 用户账号
     * @param channel 渠道标识
     * @return 用户信息响应
     */
    public LanJinUserInfoResp getUserInfo(String account, String channel) {
        String timestamp = String.valueOf(DateUtil.currentSeconds());

        LanJinUserInfoReq request = new LanJinUserInfoReq();
        request.setAccount(account);
        request.setChannel(channel);
        request.setStamp(timestamp);
        request.setSign(generateSignature(JSONObject.from(request), lanJinProperties.getSignKey()));

        log.info("{}查看用户信息开始，请求参数：{}", LOG_TAG, JSON.toJSONString(request));

        String url = lanJinProperties.getHost() + lanJinProperties.getUserInfoPath();

        Map<String, Object> formData = JSONObject.from(request);

        String responseBody = HttpUtil.createPost(url)
                .form(formData)
                .timeout(lanJinProperties.getTimeout())
                .execute()
                .body();

        log.info("{}查看用户信息响应：{}", LOG_TAG, responseBody);

        LanJinUserInfoResp result = JSON.parseObject(responseBody, LanJinUserInfoResp.class);

        log.info("{}查看用户信息完成，响应结果：{}", LOG_TAG, JSON.toJSONString(result));
        return result;
    }

    /**
     * 发放道具
     *
     * @param account 用户账号
     * @param goodsId 道具类型
     * @param channel 渠道标识 ios 和  android
     * @return 发放道具响应
     */
    public LanJinSendItemResp sendItem(String account, String goodsId, String channel, String order) {
        String timestamp = String.valueOf(DateUtil.currentSeconds());


        LanJinSendItemReq request = new LanJinSendItemReq();
        request.setAccount(account);
        request.setCardtype(goodsId);
        request.setChannel(channel);
        request.setOrder(order);
        request.setStamp(timestamp);
        request.setSign(generateSignature(JSONObject.from(request), lanJinProperties.getSignKey()));

        log.info("{}发放道具开始，请求参数：{}", LOG_TAG, JSON.toJSONString(request));

        String url = lanJinProperties.getHost() + lanJinProperties.getSendItemPath();

        Map<String, Object> formData = JSONObject.from(request);

        HttpRequest httpRequest = HttpUtil.createPost(url)
                .form(formData)
                .timeout(lanJinProperties.getTimeout());
        log.info("{}方法道具请求{}", LOG_TAG, httpRequest);
        String responseBody = httpRequest
                .execute()
                .body();

        log.info("{}发放道具响应：{}", LOG_TAG, responseBody);

        LanJinSendItemResp result = JSON.parseObject(responseBody, LanJinSendItemResp.class);

        log.info("{}发放道具完成，响应结果：{}", LOG_TAG, JSON.toJSONString(result));
        return result;
    }

    /**
     * 生成签名
     *
     * @param params  参数Map
     * @param signKey 签名密钥
     * @return 生成的MD5签名
     */
    public String generateSignature(Map<String, Object> params, String signKey) {
        // 过滤空值参数并按key升序排序
        StringBuilder signBuilder = new StringBuilder();
        params.entrySet().stream()
                .filter(entry -> entry.getValue() != null && StringUtils.isNotBlank(entry.getValue().toString()))
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> signBuilder.append(entry.getKey()).append("=").append(entry.getValue()));

        // 追加签名密钥
        signBuilder.append(signKey);

        String signString = signBuilder.toString();
        String signature = DigestUtil.md5Hex(signString);

        log.debug("{}生成签名，原字符串：{}，签名结果：{}", LOG_TAG, signString, signature);
        return signature;
    }

}
