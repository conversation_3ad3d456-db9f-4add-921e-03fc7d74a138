package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.bo.TagGroupBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopPrettyPhonePool;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2024/9/9 20:31
 */
public interface EccZopPrettyPhonePoolMapper extends BaseMapper<EccZopPrettyPhonePool> {

    void saveOrUpdateTime(@Param("list") List<EccZopPrettyPhonePool> prettyPhonePoolList);

    default Integer countBySpProdIdAndNoUsed(Integer spProdId, String spGoodsId) {
        return selectCount(Wrappers.lambdaQuery(EccZopPrettyPhonePool.class)
                .eq(EccZopPrettyPhonePool::getSpProdId, spProdId)
                .eq(EccZopPrettyPhonePool::getSpGoodsId, spGoodsId)).intValue();
    }

    /**
     * 按评分排名后删除排名大于maxScoreRank的号码 评分一样的按id倒序（刷新老号码）
     */
    int deleteByScoreRankAbove(@Param("spProdId") Integer spProdId, @Param("spGoodsId") String spGoodsId, @Param("maxScoreRank") Integer maxScoreRank);

    default int removeByPhone(String phone) {
        return delete(Wrappers.lambdaQuery(EccZopPrettyPhonePool.class)
                .eq(EccZopPrettyPhonePool::getPhone, phone));
    }

    /**
     * 批量更新评分和标签
     *
     * @param recordsToUpdate 待更新的记录
     */
    void batchUpdateScoreAndTag(@Param("list") List<EccZopPrettyPhonePool> recordsToUpdate);

    /**
     * 查询标签组
     */
    List<TagGroupBO> selectTagGroup(@Param("spProdId") Integer spProdId, @Param("spGoodsId") String spGoodsId, @Param("provinceCode") String provinceCode, @Param("cityCode") String cityCode);

    /**
     * @param page     分页
     * @param spProdId 运营商产品id
     * @param tag      标签
     * @return 分页查询号码
     */
    IPage<EccZopPrettyPhonePool> pageByTag(IPage<EccZopPrettyPhonePool> page, @Param("spProdId") Integer spProdId, @Param("spGoodsId") String spGoodsId, @Param("provinceCode") String provinceCode, @Param("cityCode") String cityCode, @Param("tag") String tag);

    void removeExpirePrettyPhonePool(@Param("hours") int hours);
}