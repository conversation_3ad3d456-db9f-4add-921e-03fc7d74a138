package com.yuelan.hermes.quanyi.config;

import com.chinaunicom.zop.api.base.logger.ZopLogger;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> 2024/4/29 下午5:44
 * <p>
 * 第三方zopjar中发现的 日志接口
 */
@Slf4j
public class ZopLoggerImpl implements ZopLogger {

    @Override
    public void info(String s) {
        log.info(s);
    }

    @Override
    public void info(String s, Throwable throwable) {
        log.info(s, throwable);
    }

    @Override
    public void debug(String s) {
        log.debug(s);
    }

    @Override
    public void debug(String s, Throwable throwable) {
        log.debug(s, throwable);
    }

    @Override
    public void error(String s) {
        log.error(s);
    }

    @Override
    public void error(String s, Throwable throwable) {
        log.error(s, throwable);
    }
}
