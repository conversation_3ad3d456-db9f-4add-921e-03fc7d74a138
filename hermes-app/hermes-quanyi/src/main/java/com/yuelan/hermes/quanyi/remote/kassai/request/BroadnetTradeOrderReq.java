package com.yuelan.hermes.quanyi.remote.kassai.request;

import lombok.Data;

/**
 * 下单请求
 */
@Data
public class BroadnetTradeOrderReq {

    /**
     * 所选号码
     */
    private String orderMobile;

    /**
     * 用户名称
     */
    private String certificateName;

    /**
     * 身份证号
     */
    private String certificateNumber;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 配送区编码
     */
    private String sendDistrictCode;

    /**
     * 号码所属市编码
     */
    private String goodsCityCode;

    /**
     * 配送详细地址
     */
    private String address;

    /**
     * 号码验证码
     */
    private String numberCheckCode;

    /**
     * 渠道订单号
     */
    private String channelSeqId;

    /**
     * 短信验证码
     */
    private String sms;

    /**
     * 渠道销售号
     */
    private String sellerId;

    /**
     * 推广编码
     */
    private String linkNum;

    /**
     * api回传参数 特定渠道专用
     */
    private ToutiaoRecord toutiaoRecord;

    /**
     * 渠道落地页（用户下单页）url，长度400字符以内
     */
    private String sourceUrl;
}
