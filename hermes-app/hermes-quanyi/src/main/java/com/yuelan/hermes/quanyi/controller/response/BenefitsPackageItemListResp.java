package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.constant.SupplierGoodsParameters;
import com.yuelan.hermes.quanyi.common.enums.DispatchTimingEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageItemDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class BenefitsPackageItemListResp {

    @Schema(description = "权益包id")
    private Long packageId;

    @Schema(description = "最大兑换次数")
    private Integer redemptionLimit;

    @Schema(description = "直发关联商品和关系")
    private List<BenefitsPackageItemResp> rechargeList;

    @Schema(description = "选发关联商品和关系")
    private List<BenefitsPackageItemResp> chooseList;

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class BenefitsPackageItemResp extends BenefitItemResp {

        @Schema(description = "关联id")
        private Long packageItemId;

        @Schema(description = "排序")
        private Integer sort;

        public static BenefitsPackageItemResp buildResp(BenefitsPackageItemDO packageItemDO, BenefitItemDO itemDO) {
            BenefitsPackageItemResp resp = new BenefitsPackageItemResp();
            resp.setBenefitItemId(itemDO.getBenefitItemId());
            resp.setBenefitName(itemDO.getBenefitName());
            resp.setBenefitCode(itemDO.getBenefitCode());
            resp.setBenefitType(itemDO.getDeliveryType());
            resp.setItemImg(itemDO.getItemImg());
            resp.setCostPrice(BigDecimal.valueOf(itemDO.getCostPrice() / 100));
            resp.setSellingPrice(BigDecimal.valueOf(itemDO.getSellingPrice() / 100));
            resp.setSupplierId(itemDO.getSupplierId());
            resp.setSupplierName(itemDO.getSupplierName());
            resp.setSupplierGoodsParam(itemDO.getSupplierGoodsParam());
            resp.setStatus(itemDO.getStatus());
            if (itemDO.getSupplierGoodsParam() != null) {
                JSONObject jsonObject = JSONObject.parseObject(itemDO.getSupplierGoodsParam());
                resp.setSupplierGoodsNo(jsonObject.getString(SupplierGoodsParameters.GOODS_CODE.getKey()));
                resp.setParValue(jsonObject.getBigDecimal(SupplierGoodsParameters.PAR_VALUE.getKey()));
            }
            resp.setPackageItemId(packageItemDO.getPackageItemId());
            resp.setSort(packageItemDO.getSort());
            return resp;
        }
    }

    public static BenefitsPackageItemListResp buildResp(BenefitsPackageDO packageDO, List<BenefitsPackageItemDO> list, List<BenefitItemDO> itemList) {
        BenefitsPackageItemListResp resp = new BenefitsPackageItemListResp();
        resp.setPackageId(packageDO.getPackageId());
        resp.setRedemptionLimit(packageDO.getRedemptionLimit());
        if (list.isEmpty() || itemList.isEmpty()) {
            return resp;
        }

        List<BenefitsPackageItemResp> rechargeList = new ArrayList<>();
        List<BenefitsPackageItemResp> chooseList = new ArrayList<>();
        Map<Long, BenefitItemDO> itemMap = itemList.stream().collect(Collectors.toMap(BenefitItemDO::getBenefitItemId, obj -> obj));
        for (BenefitsPackageItemDO packageItemDO : list) {
            BenefitItemDO itemDO = itemMap.get(packageItemDO.getBenefitItemId());
            BenefitsPackageItemResp itemResp = BenefitsPackageItemResp.buildResp(packageItemDO, itemDO);
            if (DispatchTimingEnum.RECHARGE.getCode().equals(packageItemDO.getDispatchTiming())) {
                rechargeList.add(itemResp);
            }
            if (DispatchTimingEnum.CHOOSE.getCode().equals(packageItemDO.getDispatchTiming())) {
                chooseList.add(itemResp);
            }
        }
        resp.setRechargeList(rechargeList);
        resp.setChooseList(chooseList);
        return resp;
    }

}
