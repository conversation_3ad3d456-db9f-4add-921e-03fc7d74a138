package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualSkuDO;
import com.yuelan.hermes.quanyi.controller.request.GoodsVirtualStockReq;
import com.yuelan.hermes.quanyi.controller.response.GoodsVirtualSkuSearchRsp;
import com.yuelan.hermes.quanyi.controller.response.GoodsVirtualStockRsp;
import com.yuelan.hermes.quanyi.controller.response.GoodsVirtualStockStatisticsRsp;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface GoodsVirtualSkuMapper extends BaseMapper<GoodsVirtualSkuDO> {
    int batchInsert(@Param("list") List<GoodsVirtualSkuDO> list);

    int deleteByIdIn(@Param("ids") Collection<Long> skuIds);

    List<GoodsVirtualSkuDO> findByGoodsId(@Param("goodsId") Long goodsId);

    Long countByGoodsVirtualStockReq(@Param("req") GoodsVirtualStockReq req);

    List<GoodsVirtualStockRsp> pageByGoodsVirtualStockReq(@Param("req") GoodsVirtualStockReq req);

    GoodsVirtualStockStatisticsRsp statisticsByGoodsVirtualStockReq(@Param("req") GoodsVirtualStockReq req);

    GoodsVirtualSkuDO findOne(Long skuId);

    int updateStatusById(@Param("goodsId") Long goodsId, @Param("status") Integer status);

    /**
     * 游标查询未删除的sku
     */
    List<Long> cursorAll(@Param("minId") Long minId);

    List<GoodsVirtualSkuDO> findByGoodsIdIn(@Param("goodsIds") Collection<Long> goodsIds);

    GoodsVirtualSkuDO findBySkuNo(@Param("skuNo") String skuNo);

    List<GoodsVirtualSkuSearchRsp> searchSku(@Param("goodsId") Long goodsId, @Param("skuId") Long skuId, @Param("keyword") String keyword);
}