package com.yuelan.hermes.quanyi.remote.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR> 2025/1/23
 * @since 2025/1/23
 * <p>
 * 网易下发权益请求
 */
@Data
public class NetEaseGrantBenefitsReq {

    /**
     * 供应商订单号
     */
    @JSONField(name = "orderid")
    private String orderId;

    /**
     * ⽤⼾⼿机号（base64）
     */
    private String tel;

    /**
     * 游戏权益id（由游戏提 供）
     */
    @JSONField(name = "productid")
    private String productId;

    /**
     * 游戏权益道具id（由游 戏提供，如未提供则不 传）
     */
    @JSONField(name = "sub_item")
    private String subItem;

    /**
     * 供应商侧权益的周期， 防⽌同⼀周期内权益重 复通知发放（计费会做
     * 同周期内的发放次数限 制）
     * 例如：202501
     */
    private String period;

    /**
     * 权益过期时间（unix时间戳，秒级）
     */
    @JSONField(name = "expired_time")
    private Long expiredTime;

    /**
     * 分发渠道标识，由计费提供
     */
    @JSONField(name = "app_channel")
    private String appChannel;

    /**
     * 接收权益的⻆⾊id
     */
    @JSONField(name = "userid")
    private String userId;


}
