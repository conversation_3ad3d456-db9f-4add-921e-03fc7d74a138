package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2025/4/21
 * @since 2025/4/21
 */
public interface BenefitsPackageItemMapper extends BaseMapper<BenefitsPackageItemDO> {

    /**
     * 查询权益包下的权益项
     *
     * @param packageId 权益包ID
     * @return 权益项列表
     */
    List<BenefitItemDO> listBenefitsByPackageId(@Param("packageId") Long packageId);
}