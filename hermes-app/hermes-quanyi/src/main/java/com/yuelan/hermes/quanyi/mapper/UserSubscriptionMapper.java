package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.UserSubDO;
import org.apache.ibatis.annotations.Param;

public interface UserSubscriptionMapper extends BaseMapper<UserSubDO> {

    UserSubDO selectByPhoneAndProduct(@Param("phone") String phone, @Param("productId") Long productId);

    int updateByPhoneAndProduct(UserSubDO userSubDO);

    int autoUpdateToNoSubStatus();
}