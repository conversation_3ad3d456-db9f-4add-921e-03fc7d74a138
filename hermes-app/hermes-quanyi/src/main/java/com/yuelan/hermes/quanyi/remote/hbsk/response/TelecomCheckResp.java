package com.yuelan.hermes.quanyi.remote.hbsk.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Objects;

/**
 * 电信用户检查响应
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class TelecomCheckResp {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String text;

    /**
     * 是否为电信用户
     */
    private Boolean ext;

    /**
     * 判断是否成功
     */
    @JSONField(serialize = false)
    public boolean isBizSuccess() {
        return code != null && code == 200 && Objects.equals(ext, true);
    }


}
