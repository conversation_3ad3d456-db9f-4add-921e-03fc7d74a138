package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BwCreateOrderV2Req extends BwBaseReq {
    /**
     * 产品编号（由变蛙提供）
     */
    private Long productid;
    /**
     * 面值编号（由变蛙提供）
     */
    private Long classid;
    /**
     * 充值帐号
     */
    private String account;
    /**
     * 商户订单号
     */
    private String out_trade_no;
    /**
     * 订单渠道（商户自定义）
     */
    private String channel;
    /**
     * 订单异步通知地址
     */
    private String notify_url;
    /**
     * 用户下单IP
     */
    private String userip;


}
