package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccRedeemCodeDO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> 2024/5/13 下午4:22
 */
public interface EccRedeemCodeDOMapper extends MPJBaseMapper<EccRedeemCodeDO> {
    default EccRedeemCodeDO selectOneUnused(Long goodsId) {
        return selectOne(Wrappers.<EccRedeemCodeDO>lambdaQuery().
                eq(EccRedeemCodeDO::getGoodsId, goodsId)
                .eq(EccRedeemCodeDO::getRedeemCodeStatus, 0)
                .last("limit 1")
        );
    }

    /**
     * 更新兑换码状态为已使用
     */
    default boolean updateSetCodeIsUsed(Long codeId) {
        return update(Wrappers.<EccRedeemCodeDO>lambdaUpdate()
                .set(EccRedeemCodeDO::getRedeemCodeStatus, 1)
                .eq(EccRedeemCodeDO::getRedeemCodeStatus, 0)
                .eq(EccRedeemCodeDO::getCodeId, codeId)
        ) > 0;
    }

    /**
     * 更新一张兑换码的使用状态 满足商品id和未过期
     */
    int updateOneUsedByGoodsIdAndValidity(@Param("itemNo") String itemNo, @Param("goodsId") Long goodsId);

    /**
     * 根据订单号查询兑换码
     */
    default EccRedeemCodeDO selectByItemNo(String itemNo) {
        return selectOne(Wrappers.<EccRedeemCodeDO>lambdaQuery()
                .eq(EccRedeemCodeDO::getItemNo, itemNo));
    }
}