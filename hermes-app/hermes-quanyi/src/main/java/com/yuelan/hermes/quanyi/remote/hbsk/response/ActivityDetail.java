package com.yuelan.hermes.quanyi.remote.hbsk.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 活动详细信息
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class ActivityDetail {

    /**
     * 活动ID
     */
    private Integer activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 开始时间
     */
    private Long beginTimes;

    /**
     * 结束时间
     */
    private Long endTimes;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 结果文本
     */
    private String resultText;

    /**
     * 模块ID
     */
    private Integer moduleId;

    /**
     * 验证码开关
     */
    private Integer captchaSwitch;

    /**
     * 弹框开关
     */
    private Integer popFrameSwitch;

    /**
     * 弹框URL
     */
    private String popFrameUrl;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 协议标题
     */
    private String protocolTitle;

    /**
     * 协议URL
     */
    private String protocolUrl;

    /**
     * 协议开关
     */
    private Integer protocolSwitch;

    /**
     * 背景颜色
     */
    private String backgroundColor;

    /**
     * 素材信息
     */
    private ActivityMaterials materials;

    /**
     * 商品列表
     */
    private List<ActivityGood> goods;

    /**
     * 标签列表
     */
    private List<String> labels;

    /**
     * 判断活动是否有效
     */
    @JSONField(serialize = false)
    public boolean isActive() {
        return status != null && status == 1;
    }

    /**
     * 判断是否需要验证码
     */
    public boolean needCaptcha() {
        return captchaSwitch != null && captchaSwitch == 1;
    }

    /**
     * 判断是否需要协议
     */
    public boolean needProtocol() {
        return protocolSwitch != null && protocolSwitch == 1;
    }
}
