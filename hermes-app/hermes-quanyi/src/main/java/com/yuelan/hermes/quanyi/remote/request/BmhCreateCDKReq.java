package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/1/18
 * @description BmhCreateCDKReq
 */

@NoArgsConstructor
@Data
public class BmhCreateCDKReq {

    /**
     * cdk类型:1(会员CDK)
     */
    private int type;
    /**
     * 权益skuCode
     */
    private String skuCode;
    /**
     * CDK数量
     */
    private long num;
    /**
     * cdk核销回调地址,如果不传则核销cdk时不进行回调
     */
    private String callbackUrl;
}
