package com.yuelan.hermes.quanyi.remote.hbsk.response;

import lombok.Data;

/**
 * <AUTHOR> 2025/7/18
 * @since 2025/7/18
 * code true String 0000 成功
 * description true String 描述
 */
@Data
public class HuBeiShiKeCallBackResp {

    private String code;
    private String description;

    public static HuBeiShiKeCallBackResp success() {
        HuBeiShiKeCallBackResp resp = new HuBeiShiKeCallBackResp();
        resp.setCode("0000");
        resp.setDescription("成功");
        return resp;
    }

    public static HuBeiShiKeCallBackResp error(String description) {
        HuBeiShiKeCallBackResp resp = new HuBeiShiKeCallBackResp();
        resp.setCode("9999");
        resp.setDescription(description);
        return resp;
    }

}
