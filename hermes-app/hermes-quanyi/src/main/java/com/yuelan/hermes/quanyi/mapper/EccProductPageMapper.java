package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductPageDO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> 2025/5/22
 * @since 2025/5/22
 */
public interface EccProductPageMapper extends BaseMapper<EccProductPageDO> {

    /**
     * 更新落地页的激活状态
     *
     * @param prodId 产品ID
     * @param pageId 落地页ID
     * @return 更新的行数
     */
    int updateSetActivated(@Param("prodId") Long prodId, @Param("pageId") Long pageId);

}