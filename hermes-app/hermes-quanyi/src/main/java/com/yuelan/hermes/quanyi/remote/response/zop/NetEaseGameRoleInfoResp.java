package com.yuelan.hermes.quanyi.remote.response.zop;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * <AUTHOR> 2025/1/24
 * @since 2025/1/24
 * <p>
 * 网易游戏角色信息响应
 */
@Data
public class NetEaseGameRoleInfoResp {

    private Integer code;

    private String msg;

    private RoleInfo data;

    public boolean bizSuccess() {
        return code != null && code == 1000;
    }

    @Data
    public static class RoleInfo {


        /**
         * 计费aid
         */
        private String aid;

        /**
         * 玩家渠道帐号
         */
        private String username;

        /**
         * 游戏角色id
         */
        private String roleid;

        /**
         * 角色昵称
         */
        private String rolename;

        /**
         * 游戏⻆⾊所在区服id
         */
        private int hostid;

        /**
         * ⼩区服hostid(如果存在⼩区服，通过发货接⼝private_param通知产品)
         */
        private int sub_hostid;

        /**
         * 游戏发货服，若查⻆有返回该字 段；则后续下单请求使⽤该字段 作为游戏服id传递参数；若⽆该 字段，则正常使⽤hostid作为游 戏服id传递下单参数
         */
        private int ship_hostid;

        /**
         * 游戏⻆⾊所在区服名称
         */
        private String hostname;

        /**
         * 游戏⻆⾊所在的平台 （ios/ad/pc）
         */
        private String platform;

        /**
         * 玩家⻆⾊的登陆渠道
         */
        private String login_channel;

        /**
         * 游戏分发渠道,例如： app_store，google_play，有运 营代号的例如： g0naxx2kr@app_store
         */
        private String app_channel;

        /**
         * 扩展字段内涵等级信息
         */
        private JSONObject attr;

        /**
         * ⻆⾊所属地区iso-alpha2编码， 如⽇本：JP，韩国：KR等，⽤于 相关营销活动资格判定
         */
        private String alpha2;

        /**
         * ⻆⾊其他信息
         */
        private String extra;

    }
}
