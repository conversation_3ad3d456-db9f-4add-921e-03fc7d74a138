package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderItemDO;
import com.yuelan.hermes.quanyi.controller.request.BenefitOrderItemListReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2024/4/9 下午5:25
 */
public interface BenefitOrderItemDOMapper extends MPJBaseMapper<BenefitOrderItemDO> {

    default List<BenefitOrderItemDO> listByOrderNo(String orderNo) {
        return selectList(Wrappers.<BenefitOrderItemDO>lambdaQuery().eq(BenefitOrderItemDO::getOrderNo, orderNo));
    }

    default List<BenefitOrderItemDO> listByPhoneAndProdId(String phone, Long prodId) {
        return selectList(Wrappers.<BenefitOrderItemDO>lambdaQuery()
                .eq(BenefitOrderItemDO::getPhone, phone)
                .eq(BenefitOrderItemDO::getProdId, prodId)
        );
    }

    default BenefitOrderItemDO selectOneByItemNo(String itemNo) {
        return selectOne(Wrappers.<BenefitOrderItemDO>lambdaQuery()
                .eq(BenefitOrderItemDO::getItemNo, itemNo));
    }

    IPage<BenefitOrderItemDO> selectOrderItemAndOrder(IPage<BenefitOrderItemDO> page, @Param("req") BenefitOrderItemListReq req);
}