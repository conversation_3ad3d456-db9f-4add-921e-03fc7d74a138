package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.commons.util.ExecutorServiceUtils;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.biz.service.BenefitPlatformService;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.NmgCmccPayPkgEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.NmCmccProperties;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR> 2025/4/1
 * @since 2025/4/1
 * <p>
 * 内蒙古移动权益包对接
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NmCmccManager {

    public static final String TAG = "[内蒙古移动]";
    private static final String TRUE = "success";
    /**
     * 随机码下发的method
     */
    private static final String ABILITY_CODE_SMS = "CRM_CS_SRVUPD_SMS_SEND_002";

    /**
     * 随机码校验的method
     */
    private static final String CHECK_CODE_SMS = "CRM_CS_SRVUPD_SMS_CHECK_001";

    /**
     * 合作电子渠道订购信息回传
     */
    private static final String ORDER_INFO_BACK = "CRM_ORD_SAVE_CEC_ORDERINFO_001";

    /**
     * 用户已订购商品查询
     */
    private static final String QUERY_ORDER_INFO = "CRM_SO_INF_PRODORDER_QRY_005";

    /**
     * 个人业务商品受理
     */
    private static final String PERSONAL_BUSINESS_ACCEPT = "CRM_SO_GENACC_OFFER_OFFACC_001";


    private final NmCmccProperties nmCmccProperties;
    private final BenefitOrderLogService benefitOrderLogService;
    private final BenefitOrderDOService benefitOrderDOService;

    @Resource
    @Lazy
    private BenefitPlatformService benefitPlatformService;

    /**
     * 请求下发随机码
     */
    public boolean getRandomCode(String phone, String offerId, NmgCmccPayPkgEnum pkgEnum) throws Exception {
        // 方法核心参数
        JSONObject businessInfo = new JSONObject() {{
            put("ACCESS_NUM", phone);
            // 业务代码 0001：新随机密码验证功能申请
            put("BUSI_CODE", "0001");
            // 商品ID
            put("OFFER_ID", offerId);
            // 办理渠道名称
            put("CHANNEL_NAME", nmCmccProperties.getProperties(pkgEnum).getChannelName());
        }};

        String accessToken = getAccessToken(phone, pkgEnum);
        if (StringUtils.isEmpty(accessToken)) {
            log.error("获取accessToken失败");
            return false;
        }
        String serialNo = IdUtil.fastSimpleUUID();
        String methodUrl = getMethodUrl(ABILITY_CODE_SMS, accessToken, serialNo, pkgEnum);
        Req req = buildReqBody(serialNo, businessInfo, pkgEnum);
        if (Objects.isNull(req)) {
            log.error("随机码下发请求参数为空");
            return false;
        }
        log.info("随机码下发请求参数: {}", JSON.toJSONString(req));
        //加密
        String encodeReqBody = SecurityUtils.encodeAES256HexUpper(JSON.toJSONString(req), SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
        if (StringUtils.isEmpty(encodeReqBody)) {
            log.error("随机码下发请求参数加密失败");
            return false;
        }
        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, phone);
        args.setReqParams(JSON.toJSONString(req));
        HttpResponse response = commonPostJson(methodUrl, encodeReqBody, args, (body) -> {
            try {
                return SecurityUtils.decodeAES256HexUpper(body, SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
            } catch (Exception ignored) {
                return "解密失败";
            }
        });
        if (!response.isOk()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "系统繁忙，请稍后再试");
        }

        //解密响应结果
        String decrypt = SecurityUtils.decodeAES256HexUpper(response.body(), SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
        if (StringUtils.isEmpty(decrypt)) {
            log.error("随机码下发响应结果解密失败");
            return false;
        }
        log.info("随机码下发响应结果: {}", decrypt);
        // 解析响应结果
        Resp resp = JSON.parseObject(decrypt, Resp.class);
        JSONObject busiInfo = resp.getRspParam().getBusinessInfo();
        String password = busiInfo.getString("PASSWORD");
        String message = resp.getRspParam().getPubInfo().getMessage();

        if (StringUtils.isEmpty(password)) {
            log.error("随机码下发失败，未获取到随机码");
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "随机码下发失败");
        }
        log.info("随机码下发成功，随机码: {}", password);
        //将随机码存入redis中
        String key = RedisKeys.getNmgYdRandomCodeKey(phone);
        RedisUtils.setCacheObject(key, password, Duration.ofMinutes(10));
        if (TRUE.equals(message)) {
            return true;
        } else {
            log.error(TAG + "随机码下发失败.手机号:{} 失败原因:{}", phone, JSON.toJSONString(resp));
            throw BizException.create(BizErrorCodeEnum.SMS_SEND_ERROR, "随机码下发失败");
        }
    }


    /**
     * 随机码的校验
     */
    public BenefitPayResultBO checkRandomCode(String phone, String randomCode, NmgCmccPayPkgEnum pkgEnum) throws Exception {
        BenefitPayResultBO benefitPayResultBO = new BenefitPayResultBO();
        benefitPayResultBO.setSuccess(false);
        // 构建业务信息
        JSONObject businessInfo = new JSONObject() {{
            put("ACCESS_NUM", phone);
            put("VERIFY_NUM", randomCode);
            // 业务代码 0001：新随机密码验证功能申请
            put("BUSI_CODE", "0001");
        }};

        String accessToken = getAccessToken(phone, pkgEnum);
        if (StringUtils.isEmpty(accessToken)) {
            log.error("获取accessToken失败");
            return benefitPayResultBO;
        }
        String serialNo = IdUtil.fastSimpleUUID();
        String methodUrl = getMethodUrl(CHECK_CODE_SMS, accessToken, serialNo, pkgEnum);

        Req req = buildReqBody(serialNo, businessInfo, pkgEnum);
        if (Objects.isNull(req)) {
            log.error("随机码验证请求参数为空");
            return benefitPayResultBO;
        }
        log.info("随机码验证请求参数: {}", JSON.toJSONString(req));
        String encodeReqBody = SecurityUtils.encodeAES256HexUpper(JSON.toJSONString(req), SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));

        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_CHECK, phone);
        args.setReqParams(JSON.toJSONString(req));
        HttpResponse response = commonPostJson(methodUrl, encodeReqBody, args, (body) -> {
            try {
                return SecurityUtils.decodeAES256HexUpper(body, SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
            } catch (Exception ignored) {
                return "解密失败";
            }
        });
        if (!response.isOk()) {
            log.error("随机码校验请求失败，响应状态码: {}", response.getStatus());
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "系统繁忙，请稍后再试");
        }


        String decrypt = SecurityUtils.decodeAES256HexUpper(response.body(), SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
        log.info("随机码校验响应结果: {}", decrypt);
        Resp resp = JSON.parseObject(decrypt, Resp.class);
        // 解析响应结果
        JSONObject busiInfo = resp.getRspParam().getBusinessInfo();
        String verifyResult = busiInfo.getString("VERFY_RESULT");
        String verifyCode = busiInfo.getString("VERFY_CODE");
        log.info("随机码校验结果: {}, 原因code: {}", verifyResult, verifyCode);

        if ("N".equals(verifyResult)) {
            // 校验不通过，根据verifyCode给出具体原因
            switch (verifyCode) {
                case "1":
                    return benefitPayResultBO.setMessage("错误次数已达3次");
                case "2":
                    return benefitPayResultBO.setMessage("已超时，随机码失效");
                case "3":
                    return benefitPayResultBO.setMessage("验证码错误");
                default:
                    return benefitPayResultBO.setMessage("校验失败");
            }
        }
        // 校验通过
        log.info("随机码校验通过");
        return benefitPayResultBO.setSuccess(true);
    }


    /**
     * 订购信息回传
     */
    public BenefitPayResultBO orderInfoBack(String phone, String orderId, NmgCmccPayPkgEnum pkgEnum) throws Exception {
        BenefitPayResultBO resultBO = new BenefitPayResultBO();
        resultBO.setSuccess(false);
        JSONObject businessInfo = new JSONObject() {{
            put("ACCESS_NUM", phone);
            put("ORDER_ID", orderId);
            put("LINK_URL", "https://h5.hzyuelan.com/benefit/landingPage/j6oBTlhq?channelId=22");
            put("APPLY_BROWSE_APP_NAME", "巨量引擎");
            put("ORDER_BUSINESS_NAME", pkgEnum.getName());
            put("ORDER_BUSINESS_CODE", "************");
            put("APPLY_IP", "************");
            put("BUSINESS_APPLY_TIME", LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy-MM-dd"));
            put("REMARKS", "");
        }};

//        int i = 10/0;

        String accessToken = getAccessToken(phone, pkgEnum);
        if (StringUtils.isEmpty(accessToken)) {
            log.error("获取accessToken失败");
            return resultBO;
        }
        String serialNo = IdUtil.fastSimpleUUID();
        String methodUrl = getMethodUrl(ORDER_INFO_BACK, accessToken, serialNo, pkgEnum);

        Req req = buildReqBody(serialNo, businessInfo, pkgEnum);
        if (Objects.isNull(req)) {
            log.error("订购信息回传请求参数为空");
            return resultBO;
        }
        log.info("订购信息回传请求参数: {}", JSON.toJSONString(req));
        String encodeReqBody = SecurityUtils.encodeAES256HexUpper(JSON.toJSONString(req), SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
        HttpResponse response = commonPostJson(methodUrl, encodeReqBody, null, null);
        if (!response.isOk()) {
            log.error("订购信息回传请求失败，响应状态码: {}", response.getStatus());
            return resultBO;
        }
        String decrypt = SecurityUtils.decodeAES256HexUpper(response.body(), SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
        log.info("订购信息回传响应结果: {}", decrypt);
        return resultBO.setSuccess(true);
    }


    /**
     * 合作电子渠道订购信息回传（带重试机制）
     */
    public void orderInfoBackWithRetry(String phone, String resultCode, NmgCmccPayPkgEnum pkgEnum) throws Exception {
        // 将 resultCode 作为 ORDER_ID 参数
        String orderId = resultCode;

        int retryCount = 0;
        boolean success = false;

        while (retryCount < 3 && !success) {
            try {
                BenefitPayResultBO resultBO = orderInfoBack(phone, orderId, pkgEnum);
                if (resultBO.getSuccess()) {
                    success = true;
                    log.info("订购信息回传成功，phone: {}, orderId: {}", phone, orderId);
                } else {
                    retryCount++;
                    if (retryCount < 3) {
                        // 重试间隔 1 分钟
                        Thread.sleep(60 * 1000);
                    }
                }
            } catch (Exception e) {
                log.error("订购信息回传失败，phone: {}, orderId: {}, retryCount: {}", phone, orderId, retryCount, e);
                retryCount++;
                if (retryCount < 3) {
                    // 重试间隔 1 分钟
                    Thread.sleep(60 * 1000);
                    //Thread.sleep(5000);
                }
            }
        }

        if (!success) {
            log.error("订购信息回传多次尝试后仍失败，phone: {}, orderId: {}", phone, orderId);
        }
    }


    /**
     * 用户已订购商品查询
     */
    public void orderInfoQuery(String phone, String qryType, String validDate, String expireDate, NmgCmccPayPkgEnum pkgEnum) throws Exception {
        //构建商品类型信息
        /*JSONObject offerTypeInfo = new JSONObject() {{
            put("OFFER_TYPE", "1");
        }};
        // 构建OFFER_TYPE_LIST
        JSONObject offerTypeList = new JSONObject() {{
            put("OFFER_TYPE_INFO", offerTypeInfo);
        }};
        //构建商品信息
        JSONObject offerInfo = new JSONObject() {{
            put("OFFER_ID", "1");
        }};
        // 构建OFFER_LIST
        JSONObject offerList = new JSONObject() {{
            put("OFFER_INFO", offerInfo);
        }};*/

        // 构建业务信息
        JSONObject businessInfo = new JSONObject() {{
            put("SUBBER_ID", "");
            put("CUST_ID", "");
            put("ACCESS_NUM", phone);
            put("OFFER_INS_ID", "");
            //0:现订购商品 1:历史订购商品 2:全部订购商品
            put("QRY_TYPE", qryType);
            // 当 QRY_TYPE 为 1 时，需要设置 VALID_DATE 和 EXPIRE_DATE
            if ("1".equals(qryType)) {
                put("VALID_DATE", validDate);
                put("EXPIRE_DATE", expireDate);
            } else {
                put("VALID_DATE", "");
                put("EXPIRE_DATE", "");
            }
            put("OFFER_TYPE_LIST", Collections.emptyList());
            put("OFFER_TYPE_INFO", "");
            //传入该节点，根据该节点过滤列表
            put("OFFER_LIST", Collections.emptyList());
            put("OFFER_TYPE", "");
            put("OFFER_INFO", "");
            put("OFFER_ID", "");
        }};
        String accessToken = getAccessToken(phone, pkgEnum);
        String serialNo = IdUtil.fastSimpleUUID();
        String methodUrl = getMethodUrl(QUERY_ORDER_INFO, accessToken, serialNo, pkgEnum);
        Req req = buildReqBody(serialNo, businessInfo, pkgEnum);
        log.info("订购信息查询请求参数: {}", JSON.toJSONString(req));
        String encodeReqBody = SecurityUtils.encodeAES256HexUpper(JSON.toJSONString(req), SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
        HttpResponse response = commonPostJson(methodUrl, encodeReqBody, null, null);
        if (!response.isOk()) {
            log.error("订购信息查询请求失败，响应状态码: {}", response.getStatus());
        }
        String decrypt = SecurityUtils.decodeAES256HexUpper(response.body(), SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
        log.info("订购信息查询响应结果: {}", decrypt);

    }


    /**
     * 商品受理
     */
    public BenefitPayResultBO goodsAccept(String phone, String offerId, String operCode, String deductFeed, String deductType, NmgCmccPayPkgEnum pkgEnum, String orderNo) throws Exception {
        BenefitPayResultBO benefitPayResultBO = new BenefitPayResultBO();
        benefitPayResultBO.setSuccess(false);
        // 构建 OFFER_INFO
        JSONObject offerInfo = new JSONObject() {{
            put("OFFER_ID", offerId);
            // 操作类型 1 订购 2 变更 3 退订
            put("OPER_CODE", operCode);
        }};

        // 构建 OFFER_LIST
        List<JSONObject> offerList = Collections.singletonList(new JSONObject() {{
            put("OFFER_INFO", offerInfo);
        }});

        // 构建业务信息
        JSONObject businessInfo = new JSONObject() {{
            put("ACCESS_NUM", phone);
            put("OFFER_LIST", offerList);
        }};

        String accessToken = getAccessToken(phone, pkgEnum);
        if (StringUtils.isBlank(accessToken)) {
            log.error("获取 access_token 失败");
            return benefitPayResultBO;
        }
        String serialNo = IdUtil.fastSimpleUUID();
        String methodUrl = getMethodUrl(PERSONAL_BUSINESS_ACCEPT, accessToken, serialNo, pkgEnum);
        Req req = buildReqBody(serialNo, businessInfo, pkgEnum);
        if (Objects.isNull(req)) {
            log.error("商品受理请求参数为空");
            return benefitPayResultBO;
        }
        log.info("商品受理请求参数: {}", JSON.toJSONString(req));

        String encodeReqBody = SecurityUtils.encodeAES256HexUpper(JSON.toJSONString(req), SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, phone, orderNo);
        args.setReqParams(JSON.toJSONString(req));
        HttpResponse response = commonPostJson(methodUrl, encodeReqBody, args, (body) -> {
            try {
                return SecurityUtils.decodeAES256HexUpper(body, SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
            } catch (Exception ignored) {
                return "解密失败";
            }
        });
        if (!response.isOk()) {
            log.error("商品受理请求失败，响应状态码: {}", response.getStatus());
            return benefitPayResultBO;
        }
        String decrypt = SecurityUtils.decodeAES256HexUpper(response.body(), SecurityUtils.decodeHexUpper(nmCmccProperties.getProperties(pkgEnum).getAppKey()));
        log.info("商品受理响应结果: {}", decrypt);
        //解析响应结果
        Resp resp = JSON.parseObject(decrypt, Resp.class);
        if (Objects.nonNull(resp.getRspParam()) && Objects.nonNull(resp.getRspParam().getPubInfo())) {
            benefitPayResultBO.setMessage(resp.getRspParam().getPubInfo().getMessage());
        }
        if (Objects.isNull(resp.getRspParam().getBusinessInfo())) {
            return benefitPayResultBO;
        }
        String resultCode = resp.getRspParam().getBusinessInfo().getString("resultCode");

        if (!StringUtils.isBlank(resultCode)) {
            // 调用合作电子渠道订购信息回传接口（带重试机制）
            orderInfoBackWithRetry(phone, resultCode, pkgEnum);
        }
        return benefitPayResultBO.setSuccess(true);
    }


    /**
     * 获取token的url
     *
     * @param redirectUri 重定向地址 好像没用待验证
     * @param pkgEnum
     * @return 完整的token请求地址
     */
    private String getTokenUrl(String redirectUri, NmgCmccPayPkgEnum pkgEnum) {
        String tokenHttpUri = "http://" + nmCmccProperties.getProperties(pkgEnum).getTokenIp() + "/aopoauth/oauth/token";
        return tokenHttpUri
                + "?grant_type=client_credentials&app_id=" + nmCmccProperties.getProperties(pkgEnum).getAppId()
                + "&app_key=" + nmCmccProperties.getProperties(pkgEnum).getAppKey() + "&redirect_uri=" + redirectUri;
    }

    /**
     * 获取能力请求的url
     *
     * @param abilityCode    能力码
     * @param token          动态获取的token
     * @param businessSerial 业务流水号
     * @param pkgEnum
     * @return 完整的能力请求地址
     */
    private String getMethodUrl(String abilityCode, String token, String businessSerial, NmgCmccPayPkgEnum pkgEnum) {
        String tokenHttpUri = "http://" + nmCmccProperties.getProperties(pkgEnum).getOppfIp() + "/oppf";
        return tokenHttpUri + "?method=" + abilityCode +
                "&format=json" +
                "&appId=" + nmCmccProperties.getProperties(pkgEnum).getAppId() +
                "&timestamp=" + LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN) +
                "&accessToken=" + token +
                "&busiSerial=" + businessSerial;
    }

    /**
     * 获取token 请求能力方法需要用
     */
    public String getAccessToken(String phone, NmgCmccPayPkgEnum pkgEnum) {
        // 先从缓存中获取token
        String cacheKey = RedisKeys.getNmCmccToken(phone);
        Object cacheVal = RedisUtils.getCacheObject(cacheKey);
        if (Objects.nonNull(cacheVal)) {
            log.info("从缓存中获取到token: {}", cacheVal);
            return String.valueOf(cacheVal);
        }
        // 如果缓存中没有token，则重新获取
        String redirectUri = "https://h5.hzyuelan.com/benefit/landingPage/j6oBTlhq?channelId=22";
        String tokenUrl = getTokenUrl(redirectUri, pkgEnum);
        HttpResponse httpResponse = commonPostJson(tokenUrl, "", null, null);
        if (!httpResponse.isOk()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "系统繁忙，请稍后再试");
        }
        JSONObject jsonObject = JSON.parseObject(httpResponse.body());

        String newToken = jsonObject.getString("access_token");
        Integer expiresIn = jsonObject.getInteger("expires_in");


        // 将新获取的token存入缓存
        RedisUtils.setCacheObject(cacheKey, newToken, Duration.ofSeconds(expiresIn - 20));
        log.info("新获取的token已存入缓存: {}", newToken);

        return newToken;
    }

    /**
     * 公共的post请求
     *
     * @param url  请求地址
     * @param body 请求体
     * @return 响应结果
     */
    private HttpResponse commonPostJson(String url, String body, BenefitOrderLog.Args args, Function<String, String> decryFun) {
//        HttpRequest post = HttpRequest.post(url);
//        post.header("Content-Type", "application/json; charset=UTF-8");
//        post.body(body);
//        post.timeout(10000);
//        log.info("发送请求:{}", post);
//        HttpResponse execute = post.execute();
//        log.info("返回结果:{}", execute);

        try {
            HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                    .contentType("application/json")
                    .body(body)
                    .timeout(10000)
                    .log(BenefitPayChannelEnum.NMG_CMCC, args);
            log.info(TAG + "请求前:params:{},{}", args == null ? "" : args.getReqParams(), requestWrapper.getHttpRequest());
            HttpResponse response = benefitOrderLogService.http(requestWrapper, decryFun);
            log.info(TAG + "请求后:params:{},{}", args == null ? "" : args.getReqParams(), response);
            return response;
        } catch (Exception e) {
            log.info(TAG + "接口:{} 请求参数:{}", url, args == null ? "" : args.getReqParams(), e);
            throw e;
        }
    }

    /**
     * 获取请求参数
     *
     * @param serialNo     请求流水号
     * @param businessInfo 业务参数
     * @param pkgEnum
     * @return 请求参数
     */
    public Req buildReqBody(String serialNo, JSONObject businessInfo, NmgCmccPayPkgEnum pkgEnum) {
        Req reqBody = new Req();
        ReqParam reqParam = new ReqParam();

        ReqParam.ReqPubInfo pubInfo = new ReqParam.ReqPubInfo();
        pubInfo.setOpCode(nmCmccProperties.getProperties(pkgEnum).getOpCode());
        pubInfo.setReqSerialNo(serialNo);
        pubInfo.setChannelId(nmCmccProperties.getProperties(pkgEnum).getChannelId());
        pubInfo.setOpOrgId(nmCmccProperties.getProperties(pkgEnum).getOpOrgId());
        reqParam.setPubInfo(pubInfo);

        reqParam.setBusinessInfo(businessInfo);
        reqBody.setReqParam(reqParam);
        return reqBody;
    }

    /**
     * 发送验证码短信
     *
     * @param reqParams
     * @param productDO
     * @return
     */
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String phone = reqParams.getMobile();
        NmgCmccPayPkgEnum pkgEnum = NmgCmccPayPkgEnum.of(productDO.getPayChannelPkgId());
        if (Objects.isNull(pkgEnum)) {
            log.error("未找到对应的支付渠道包配置，payChannelPkgId: {}", productDO.getPayChannelPkgId());
            throw BizException.create(BizErrorCodeEnum.PROD_PAYPKG_ERROR, "未找到对应的支付渠道包配置");
        }
        try {
            //运营商下发验证码
            return getRandomCode(phone, nmCmccProperties.getProperties(pkgEnum).getOfferId(), pkgEnum);
        } catch (Exception e) {
            log.error("发送验证码短信失败，phone: {}", phone, e);
            throw BizException.create(BizErrorCodeEnum.SMS_SEND_ERROR, "发送验证码短信失败");
        }
    }


    /**
     * 申请订购
     *
     * @param reqParams
     * @param productDO
     * @param orderDO
     * @return
     */
    public BenefitPayResultBO createOrder(UserBenefitOrderReq reqParams, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        BenefitPayResultBO payResultBO = new BenefitPayResultBO();
        payResultBO.setSuccess(false);
        String phone = reqParams.getMobile();
        NmgCmccPayPkgEnum pkgEnum = NmgCmccPayPkgEnum.of(productDO.getPayChannelPkgId());
        if (Objects.isNull(pkgEnum)) {
            log.error("未找到对应的支付渠道包配置，payChannelPkgId: {}", productDO.getPayChannelPkgId());
            throw BizException.create(BizErrorCodeEnum.PROD_PAYPKG_ERROR, "未找到对应的支付渠道包配置");
        }
        //从缓存中获取随机码
        String randomCode = RedisUtils.getCacheObject(RedisKeys.getNmgYdRandomCodeKey(phone));
        if (StringUtils.isBlank(randomCode)) {
            // 缓存中没有,说明缓存过期,中间间隔的时间过久
            payResultBO.setMessage("请重新发送验证码");
            return payResultBO;
        }
        // 1. 校验随机码
        try {
            payResultBO = checkRandomCode(phone, reqParams.getSmsCode(), pkgEnum);
            if (!payResultBO.getSuccess()) {
                payResultBO.setMessage("验证码错误");
                return payResultBO;
            }
        } catch (Exception e) {
            log.error(TAG + "随机码校验失败.手机号:{}", phone, e);
            payResultBO.setMessage("订购失败,请稍后重试");
            return payResultBO;
        }

        // 2. 商品受理
        try {
            payResultBO = goodsAccept(phone, nmCmccProperties.getProperties(pkgEnum).getOfferId(), "1", "0", "1", pkgEnum, orderDO.getOrderNo());
            if (!payResultBO.getSuccess()) {
                orderDO.setPayStatus(PayStatusEnum.FAIL.getCode());
//                payResultBO.setMessage("商品受理失败");
//                return payResultBO;
            } else {
                orderDO.setPayStatus(PayStatusEnum.SUCCESS.getCode());
                orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
                orderDO.setUpdateTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
//                benefitOrderDOService.updateById(orderDO);
            }
        } catch (Exception e) {
            log.error(TAG + "商品受理失败.手机号:{}", phone, e);
            payResultBO.setMessage("订购失败,请稍后重试");
            return payResultBO;
        }

        orderDO.setPayNotifyContent(JSON.toJSONString(payResultBO));
        orderDO.setPayNotifyTime(LocalDateTime.now());
        final String msg = payResultBO.getMessage();
        ExecutorServiceUtils.execute(() -> {
            try {
                // 先睡5秒,这里是为了让订单号先返回给媒体,等媒体处理好自己的流程后,再通知给媒体
                Thread.sleep(5000);
                // 通知媒体
                benefitPlatformService.notifyMediaOrderResult(orderDO, msg);
            } catch (Exception e) {
                log.info(TAG + "通知媒体失败.订单id:{}", orderDO.getOrderId(), e);
            }
        });
        return payResultBO;
    }


    @Data
    public static class Resp {

        @JSONField(name = "RSP_PARAM")
        private RespParam rspParam;

    }

    @Data
    public static class Req {

        @JSONField(name = "REQ_PARAM")
        private ReqParam reqParam;
    }


    @Data
    public static class ReqParam {
        @JSONField(name = "PUB_INFO")
        private ReqPubInfo pubInfo;


        @JSONField(name = "BUSI_INFO")
        private JSONObject businessInfo;


        @Data
        public static class ReqPubInfo {
            @JSONField(name = "OP_CODE")
            private String opCode;

            @JSONField(name = "REQ_SERIAL_NO")
            private String reqSerialNo;

            @JSONField(name = "CHANNEL_ID")
            private String channelId;

            @JSONField(name = "OP_ORG_ID")
            private String opOrgId;
        }

    }


    @Data
    public static class RespParam {

        @JSONField(name = "PUB_INFO")
        private RespPubInfo pubInfo;

        @JSONField(name = "BUSI_INFO")
        private JSONObject businessInfo;

        @Data
        public static class RespPubInfo {

            @JSONField(name = "REQ_SERIAL_NO")
            private String reqSerialNo;

            @JSONField(name = "MESSAGE")
            private String message;

            @JSONField(name = "BUSI_SERIAL_NO")
            private String businessSerialNo;

            @JSONField(name = "CODE")
            private String code;
        }

    }


    static class SecurityUtils {


        private SecurityUtils() {
        }


        public static byte[] encrypt(byte[] data, byte[] key) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(1, new SecretKeySpec(key, "AES"));
            return cipher.doFinal(data);
        }

        public static byte[] decrypt(byte[] data, byte[] key) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            cipher.init(2, new SecretKeySpec(key, "AES"));
            return cipher.doFinal(data);
        }

        public static String encodeHexUpper(byte[] data) throws UnsupportedEncodingException {
            return bytesToHexString(data).toUpperCase(Locale.US);
        }

        public static byte[] decodeHexUpper(String str) throws UnsupportedEncodingException {
            return Hex.decode(str.toLowerCase(Locale.US));
        }

        public static String decodeHexUpper(String str, String charsetName) throws UnsupportedEncodingException {
            return new String(Hex.decode(str.toLowerCase(Locale.US)), charsetName);
        }

        public static String encodeAES256HexUpper(String data, byte[] key) throws UnsupportedEncodingException, IllegalBlockSizeException, InvalidKeyException, BadPaddingException, NoSuchAlgorithmException, NoSuchPaddingException {
            return encodeHexUpper(encrypt(data.getBytes("UTF-8"), key));
        }

        public static String decodeAES256HexUpper(String data, byte[] key) throws IllegalBlockSizeException, InvalidKeyException, BadPaddingException, NoSuchAlgorithmException, NoSuchPaddingException, UnsupportedEncodingException {
            return new String(decrypt(Hex.decode(data.toLowerCase(Locale.US)), key), "UTF-8");
        }

        public static String bytesToHexString(byte[] b) {
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < b.length; ++i) {
                String hex = Integer.toHexString(b[i] & 255);
                if (hex.length() == 1) {
                    hex = '0' + hex;
                }

                sb.append(hex);
            }

            return sb.toString();
        }

    }


}
