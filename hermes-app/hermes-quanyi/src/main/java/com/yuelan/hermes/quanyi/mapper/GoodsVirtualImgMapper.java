package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualImgDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GoodsVirtualImgMapper extends BaseMapper<GoodsVirtualImgDO> {
    int insert(GoodsVirtualImgDO goodsVirtualImgDO);

    List<GoodsVirtualImgDO> findByGoodsId(Long goodsId);

    int updateImg(@Param("imgJson") String imgJson, @Param("goodsId") Long goodsId);

    String findImgByGoodsId(@Param("goodsId") Long goodsId);
}