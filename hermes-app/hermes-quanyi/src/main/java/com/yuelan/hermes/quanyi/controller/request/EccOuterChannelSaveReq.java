package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.result.enums.YesOrNoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/5/17 下午4:05
 */
@Data
public class EccOuterChannelSaveReq {

    @NotNull(message = "外部渠道ID不能为空", groups = {EditGroup.class})
    @Schema(description = "外部渠道id")
    private Long outerChannelId;


    @NotEmpty(message = "渠道名字不能为空", groups = {AddGroup.class})
    @Schema(description = "渠道名字")
    private String channelName;

    @Schema(title = "是否禁用", description = "是否禁用：0-表示正常，1-表示禁用")
    private Integer isDisabled;


    @Schema(title = "服务器白名单", description = "多个ip用英文逗号隔开")
    private String ipWhitelist;

    /**
     * 联通zop发展人编号
     */
    @Schema(description = "联通zop发展人编号")
    private String zopReferrerCode;



    public EccOuterChannelDO convert() {
        EccOuterChannelDO channelDO = new EccOuterChannelDO();
        channelDO.setOuterChannelId(outerChannelId);
        channelDO.setChannelName(channelName);
        YesOrNoEnum yesOrNoEnum = YesOrNoEnum.of(isDisabled);
        if (yesOrNoEnum != null) {
            channelDO.setIsDisabled(isDisabled);
        }
        channelDO.setZopReferrerCode(zopReferrerCode);
        channelDO.setIpWhitelist(ipWhitelist);
        return channelDO;
    }

}
