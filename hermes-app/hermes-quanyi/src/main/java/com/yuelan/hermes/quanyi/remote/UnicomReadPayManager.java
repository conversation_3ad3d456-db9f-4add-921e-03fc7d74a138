package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuelan.boot.utils.HttpServletRequestUtil;
import com.yuelan.hermes.commons.enums.BizNoPrefixEnum;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.UnicomWoPayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitUnicomPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.UnicomProperties;
import com.yuelan.hermes.quanyi.common.util.LocalBizNoPlusUtils;
import com.yuelan.hermes.quanyi.common.util.UnicomReadTrackUtil;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.request.WoReadStatusChangeReq;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR> 2024/4/7 14:25
 * 联通支付接口对接
 * 《联通沃阅读包月产品订购对接CAP第三方接入指南》
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UnicomReadPayManager {
    private static final Integer TIME_OUT = 5000;
    private static final String UNICODE_SUCCESS_CODE = "0000";

    private static final String UNICODE_RETURN_CODE = "SUCCESS";

    // 获取请求token的接口
    private static final String CLIENT_OAUTH_URL = "/oauth/client/key";
    // 创建订单请求-不支持三方合作公司的参数channelKey没有
    private static final String UNICOM_CREATE_ORDER_URL = "https://pcc.woread.com.cn/cappay/rest/order/package/beforeordervac/{}/{}/{}";
    // h5非微信容器
    private static final String WX_ORDER_URI = "/cappay/rest/order/package/wxBeforeorder/{}/{}/{}";
    // 支付宝支付
    private static final String ALIPAY_ORDER_URI = "/cappay/rest/order/package/aliBeforeorder/{}/{}/{}";

    private final UnicomProperties unicomProperties;

    private final BenefitOrderDOService benefitOrderDOService;

    private final BenefitOrderLogService benefitOrderLogService;

    /**
     * 获取请求地址，部分合作方把联通的参数直接给我们，还走联通的协议但是请求域名是合作方的
     *
     * @param uri 请求路径
     * @return 请求地址
     */
    private String buildReqUrl(UnicomProperties.UnicomConfig config, String uri) {
        return config.getDomain() + uri;
    }

    /**
     * 应用认证 并且设置缓存
     *
     * @return token值 用在其他接口中
     */
    private String clientOAuth(UnicomWoPayPkgEnum unicomWoPayPkgEnum) {
        HttpRequest request = buildClientOauthReq(unicomWoPayPkgEnum);
        log.info("发送[应用认证]请求{}", request);
        HttpResponse execute = request.execute();
        log.info("请求[应用认证]状态:{}响应:{}", execute.getStatus(), execute.body());
        if (!execute.isOk()) {
            log.error("请求[应用认证]状态:{}", execute.getStatus());
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
        }
        JSONObject respJson = JSONObject.parseObject(execute.body());
        String code = respJson.getString("code");
        String keyType = respJson.getString("key_type");
        String key = respJson.getString("key");
        Integer expiresIn = respJson.getInteger("expires_in");
        if (!UNICODE_SUCCESS_CODE.equals(code) || StrUtil.isBlank(keyType) || StrUtil.isBlank(key) || Objects.isNull(expiresIn)) {
            log.error("请求[应用认证]响应:{}", execute.body());
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
        }
        String token = keyType + " " + key;
        UnicomProperties.UnicomConfig config = unicomProperties.getProperties(unicomWoPayPkgEnum);
        String cacheKey = RedisKeys.getSyUnicomToken(config.getClientId());
        RedisUtils.setCacheObject(cacheKey, token, Duration.ofSeconds(expiresIn));
        return token;
    }

    private HttpRequest buildClientOauthReq(UnicomWoPayPkgEnum unicomWoPayPkgEnum) {
        UnicomProperties.UnicomConfig config = unicomProperties.getProperties(unicomWoPayPkgEnum);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("clientSource", "11");
        paramMap.put("clientId", config.getClientId());
        paramMap.put("clientSecret", config.getClientSecret());
        String url = buildReqUrl(config, CLIENT_OAUTH_URL);
        return HttpRequest.get(url).form(paramMap).timeout(TIME_OUT);
    }

    /**
     * 获取 请求的token
     */
    private String getToken(UnicomWoPayPkgEnum unicomWoPayPkgEnum) {
        UnicomProperties.UnicomConfig config = unicomProperties.getProperties(unicomWoPayPkgEnum);
        String cacheKey = RedisKeys.getSyUnicomToken(config.getClientId());
        Object cacheVal = RedisUtils.getCacheObject(cacheKey);
        if (Objects.nonNull(cacheVal)) {
            return String.valueOf(cacheVal);
        }
        return clientOAuth(unicomWoPayPkgEnum);
    }

    /**
     * 获取动态channelIdKey 参数
     * 从 http://m.woread.com.cn/touchactivity/marketingControlMiddle/index.action?pageid={pageId} 得到以下接口
     * 请求 http://m.woread.com.cn/touchactivity/marketingControlMiddle/getThirdPartyUrl.action 获取channelIdKey
     */
    private String getChannelIdKey(UnicomProperties.UnicomConfig config) {
        String pageId = config.getPageId();
        // 这个地址是运营商解析到我们服务器 我们服务器是合作方佳流开发的代码
        String url = "http://m.woread.com.cn/touchactivity/marketingControlMiddle/getThirdPartyUrl.action";
        // 发送 x-www-form-urlencoded 请求
        HttpRequest request = HttpRequest.post(url)
                .form("pageid", pageId)
                // 固定值
                .form("platformid", "0")
                .timeout(TIME_OUT);
        log.info("发送[获取channelIdKey]请求{}", request);
        HttpResponse execute = request.execute();
        log.info("请求[获取channelIdKey] 响应:{}", execute);
        if (!execute.isOk()) {
            log.error("请求[获取channelIdKey]状态:{}", execute.getStatus());
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
        }
        JSONObject respJson = JSONObject.parseObject(execute.body());
        String code = respJson.getString("code");
        String innerCode = respJson.getString("innercode");
        if (!UNICODE_SUCCESS_CODE.equals(code) || !UNICODE_SUCCESS_CODE.equals(innerCode)) {
            log.error("请求[获取channelIdKey]响应:{}", execute.body());
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
        }
        JSONObject messageJson = respJson.getJSONObject("message");
        String key = messageJson.getString("key");
        if (Objects.isNull(key)) {
            log.error("请求[获取channelIdKey]解析key异常");
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
        }
        return key;
    }


    /**
     * 申请订单
     * 白名单ip **************
     */
    public BenefitUnicomPayResultBO createOrder(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        Integer payChannelPkgId = productDO.getPayChannelPkgId();
        String orderNo = orderDO.getOrderNo();

        UnicomWoPayPkgEnum unicomWoPayPkgEnum = UnicomWoPayPkgEnum.of(payChannelPkgId);
        UnicomProperties.UnicomConfig config = unicomProperties.getProperties(unicomWoPayPkgEnum);
        if (unicomWoPayPkgEnum == UnicomWoPayPkgEnum.ALI_PAY) {
            return this.handleAliPay(unicomWoPayPkgEnum, config, req, productDO, orderDO);
        }
        if (unicomWoPayPkgEnum != null && unicomWoPayPkgEnum.getIgnoreSign() == 1) {
            return this.handleWxPay(unicomWoPayPkgEnum, config, req, productDO, orderDO);
        }
        // 必填。第三方客户，值为11；
        String source = "11";
        String channelId = config.getChannelId();
        String url = StrUtil.format(UNICOM_CREATE_ORDER_URL, source, req.getMobile(), channelId);
        JSONObject reqJson = new JSONObject();
        reqJson.put("productpkgid", config.getPkgId());
        reqJson.put("returl", productDO.getDistributionUrl());
        reqJson.put("bakurl", config.getNotifyUrl().trim() + "/" + orderNo);
        reqJson.put("ip", ServletUtil.getClientIP(HttpServletRequestUtil.getCurrentRequest()));
        reqJson.put("channlidkey", getChannelIdKey(config));

        // 日志参数
        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, orderDO.getPhone(), orderDO.getOrderNo());
        args.ip(reqJson.getString("ip"), "ip");

        // 发送请求
        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                .header("AuthorizationClient", getToken(unicomWoPayPkgEnum))
                .body(reqJson.toJSONString())
                .timeout(TIME_OUT)
                .log(BenefitPayChannelEnum.UNICOM, args); // 日志信息

        log.info("发送[申请订单]请求{}", requestWrapper.getHttpRequest());

        HttpResponse execute = benefitOrderLogService.http(requestWrapper);

        orderDO.setPreorderContent(StrUtil.sub(execute.body(), 0, 500));
        if (!execute.isOk()) {
            log.error("请求[申请订单]状态:{}", execute.getStatus());
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
        }
        JSONObject respJson = JSONObject.parseObject(execute.body());
        String code = respJson.getString("code");
        String innerCode = respJson.getString("innercode");
        if (!UNICODE_SUCCESS_CODE.equals(code) || !UNICODE_SUCCESS_CODE.equals(innerCode)) {
            log.error("请求[申请订单]响应:{}", execute.body());
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
        }
        JSONObject messageJson = respJson.getJSONObject("message");
        // 订单申请唯一签名，后续订购结果通知会用到此参数
        String sign = messageJson.getString("sign");
        // 订单号
        String spOrder = messageJson.getString("torder");
        // 用户订购url
        String userReqUrl = messageJson.getString("requrl");
        orderDO.setOutOrderNo(spOrder);
        orderDO.setExtraData(new JSONObject() {{
            put("sign", sign);
        }}.toString());
        BenefitUnicomPayResultBO resultBO = new BenefitUnicomPayResultBO();
        resultBO.setSuccess(true);
        resultBO.setPayUrl(userReqUrl);
        return resultBO;
    }

    // 微信支付处理
    private BenefitUnicomPayResultBO handleWxPay(UnicomWoPayPkgEnum unicomWoPayPkgEnum
            , UnicomProperties.UnicomConfig config
            , UserBenefitOrderReq req
            , BenefitProductDO productDO
            , BenefitOrderDO orderDO) {
        String tid = UUID.randomUUID().toString().replaceAll("-", "");
        this.sendEventPoint(unicomWoPayPkgEnum, EventPointEnum.STEP_1, req.getMobile(), tid, null);
        this.sendEventPoint(unicomWoPayPkgEnum, EventPointEnum.STEP_2, req.getMobile(), tid, orderDO.getOrderNo());

        String source = "11";
        String channelId = config.getChannelId();
        String url = StrUtil.format(buildReqUrl(config, WX_ORDER_URI), source, req.getMobile(), channelId);
        JSONObject reqJson = new JSONObject();
        reqJson.put("productpkgid", config.getPkgId());
        reqJson.put("body", config.getProdName());
        reqJson.put("attach", orderDO.getOrderNo());
        reqJson.put("trade_type", "MWEB");
        // 默认续订24个月
        reqJson.put("quantity", "1");
        reqJson.put("fee", config.getFree());
        if (StringUtils.isBlank(req.getSubscriptionType()) || "1".equals(req.getSubscriptionType())) {
            // 默认是连续订购
            reqJson.put("isOpenContinuePackage", "1");
            reqJson.put("plan_id", config.getPlanId());
            reqJson.put("contract_code", orderDO.getOrderNo());
            reqJson.put("contract_display_account", req.getMobile());
        } else {
            reqJson.put("isOpenContinuePackage", "0");
        }
        // 如果回跳地址为空,跳到我们这边的地址
        reqJson.put("callbackurl", StringUtils.defaultIfBlank(req.getRetUrl(), productDO.getDistributionUrl()));
        reqJson.put("notify_url", config.getNotifyUrl().trim() + "/" + orderDO.getOrderNo());
        reqJson.put("ip", ServletUtil.getClientIP(HttpServletRequestUtil.getCurrentRequest()));
        reqJson.put("channlidkey", this.getChannelIdKey(config));

        // 日志参数
        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, orderDO.getPhone(), orderDO.getOrderNo());
        args.ip(reqJson.getString("ip"), "ip");

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                .header("AuthorizationClient", getToken(unicomWoPayPkgEnum))
                .body(reqJson.toJSONString())
                .timeout(TIME_OUT)
                .log(BenefitPayChannelEnum.UNICOM, args); // 日志信息

        log.info("发送[申请订单]请求{}", requestWrapper.getHttpRequest());

        HttpResponse execute = benefitOrderLogService.http(requestWrapper);

        orderDO.setPreorderContent(StrUtil.sub(execute.body(), 0, 500));
        if (!execute.isOk()) {
            log.info("请求[申请订单]状态:{}", execute.getStatus());
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
        }
        JSONObject respJson = JSONObject.parseObject(execute.body());
        String code = respJson.getString("code");
        String innerCode = respJson.getString("innercode");
        if (!UNICODE_SUCCESS_CODE.equals(code) || !UNICODE_SUCCESS_CODE.equals(innerCode)) {
            log.info("请求[申请订单]响应:{}", execute.body());
            throw BizException.create(BizResult.error(code, respJson.getString("message")));
        }
        JSONObject messageJson = respJson.getJSONObject("message");
        // 返回状态码
        String return_code = messageJson.getString("return_code");

        // 订单申请唯一签名，后续订购结果通知会用到此参数
        String sign = messageJson.getString("sign");
        // 订单号
        String spOrder = messageJson.getString("out_trade_no");
        // 用户订购url
        String userReqUrl = messageJson.getString("reqUrl");
        orderDO.setOutOrderNo(spOrder);
        orderDO.setExtraData(new JSONObject() {{
            put("sign", sign);
        }}.toString());
        BenefitUnicomPayResultBO resultBO = new BenefitUnicomPayResultBO();
        resultBO.setSuccess(return_code.equals(UNICODE_RETURN_CODE));
        resultBO.setPayUrl(userReqUrl);

        this.sendEventPoint(unicomWoPayPkgEnum, EventPointEnum.STEP_6, req.getMobile(), tid, orderDO.getOrderNo());
        this.sendEventPoint(unicomWoPayPkgEnum, EventPointEnum.STEP_7, req.getMobile(), tid, null);
        return resultBO;
    }

    /**
     * 处理支付宝支付
     * 湖南豫信不会给续订和退订的回调
     */
    private BenefitUnicomPayResultBO handleAliPay(UnicomWoPayPkgEnum unicomWoPayPkgEnum
            , UnicomProperties.UnicomConfig config
            , UserBenefitOrderReq req
            , BenefitProductDO productDO
            , BenefitOrderDO orderDO) {

        String source = "11";
        String channelId = config.getChannelId();
        String url = StrUtil.format(buildReqUrl(config, ALIPAY_ORDER_URI), source, req.getMobile(), channelId);
        JSONObject reqJson = new JSONObject();
        reqJson.put("productpkgid", config.getPkgId());
        reqJson.put("body", config.getProdName());
        // 默认续订24个月
        reqJson.put("quantity", "1");
        reqJson.put("fee", config.getFree());
        reqJson.put("isOpenContinuePackage", StringUtils.defaultString(req.getSubscriptionType(), "1"));
        // 如果回跳地址为空,跳到我们这边的地址
        reqJson.put("returnurl", StringUtils.defaultIfBlank(req.getRetUrl(), productDO.getDistributionUrl()));
        reqJson.put("notify_url", config.getNotifyUrl().trim() + "/" + orderDO.getOrderNo());
        reqJson.put("ip", ServletUtil.getClientIP(HttpServletRequestUtil.getCurrentRequest()));

        // 日志参数
        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, orderDO.getPhone(), orderDO.getOrderNo());
        args.ip(reqJson.getString("ip"), "ip");

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                .header("AuthorizationClient", getToken(unicomWoPayPkgEnum))
                .body(reqJson.toJSONString())
                .timeout(TIME_OUT)
                .log(BenefitPayChannelEnum.UNICOM, args); // 日志信息

        log.info("发送[申请订单]请求{}", requestWrapper.getHttpRequest());

        HttpResponse execute = benefitOrderLogService.http(requestWrapper);

        orderDO.setPreorderContent(StrUtil.sub(execute.body(), 0, 500));
        if (!execute.isOk()) {
            log.info("请求[申请订单] 状态:{}", execute.getStatus());
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
        }
        JSONObject respJson = JSONObject.parseObject(execute.body());
        String code = respJson.getString("code");
        String innerCode = respJson.getString("innercode");
        if (!UNICODE_SUCCESS_CODE.equals(code) || !UNICODE_SUCCESS_CODE.equals(innerCode)) {
            log.info("请求[申请订单]响应:{}", execute.body());
            throw BizException.create(BizResult.error(code, respJson.getString("message")));
        }
        JSONObject messageJson = respJson.getJSONObject("message");
        // 订单号
        String spOrder = messageJson.getString("orderid");
        // 用户订购url
        String userReqUrl = messageJson.getString("reqUrl");
        orderDO.setOutOrderNo(spOrder);
        BenefitUnicomPayResultBO resultBO = new BenefitUnicomPayResultBO();
        resultBO.setSuccess(Boolean.TRUE);
        resultBO.setPayUrl(userReqUrl);
        return resultBO;
    }


    /**
     * 每次有订单变更
     * 运营要新插入一条订单记录,不在原来的订单上做变更
     */
    public BizResult<?> wxStatusNotify(WoReadStatusChangeReq params) {
        UnicomWoPayPkgEnum payPkgEnum = null;
        for (UnicomWoPayPkgEnum unicomWoPayPkgEnum : unicomProperties.getPkgConfigMap().keySet()) {
            if (Objects.equals(params.getProductpkgid(), unicomWoPayPkgEnum.getPkgId() + "")) {
                payPkgEnum = unicomWoPayPkgEnum;
            }
        }
        if (payPkgEnum == null) {
            log.info("查找订单失败.入参:{}", JSON.toJSON(params));
            return BizResult.error("3", "未查找到原有商品");
        }
        UnicomProperties.UnicomConfig config = unicomProperties.getProperties(payPkgEnum);

        // 校验签名 要忽略传过来签名
        Map<String, Object> map = BeanUtil.copyProperties(params, Map.class, "sign");

        String signStr = MapUtil.join(MapUtil.sort(map), "&", "=", true, "&key=" + config.getKey());
        String mySign = SecureUtil.md5(signStr).toLowerCase();

        if (!params.getSign().equals(mySign)) {
            log.info("签名校验失败。要签名的字符串:{},传过来的签名:{},生成的签名:{}", signStr, params.getSign(), mySign);
            return BizResult.error("1", "签名校验失败");
        }
        // 查询一条该手机号对应的最新的支付成功的订购记录
        BenefitOrderDO oldOrderDO = benefitOrderDOService.getOne(new LambdaQueryWrapper<BenefitOrderDO>()
                .eq(BenefitOrderDO::getPhone, params.getPhone())
                .eq(BenefitOrderDO::getOutOrderNo, params.getBusinessorderno())
                .eq(BenefitOrderDO::getPayChannelPkgId, payPkgEnum.getChannelPkgId())
                .eq(BenefitOrderDO::getPayStatus, PayStatusEnum.SUCCESS.getCode())
                .orderByDesc(BenefitOrderDO::getOrderId)
                .last("limit 1"));
        if (oldOrderDO == null) {
            log.info("查找订单失败.入参:{}", JSON.toJSON(params));
            return BizResult.error("2", "未查找到原有订单");
        }
        BenefitOrderDO benefitOrderDO = new BenefitOrderDO();
        benefitOrderDO.setOrderNo(LocalBizNoPlusUtils.genBillNo(BizNoPrefixEnum.ORDER_BENEFIT.getPrefix()));
        benefitOrderDO.setOutOrderNo(params.getBusinessorderno());
        benefitOrderDO.setDistributionChannel(oldOrderDO.getDistributionChannel());
        benefitOrderDO.setChannelId(oldOrderDO.getChannelId());
        benefitOrderDO.setChannelName(oldOrderDO.getChannelName());
        benefitOrderDO.setAdChannelId(oldOrderDO.getAdChannelId());
        benefitOrderDO.setAdChannelName(oldOrderDO.getAdChannelName());
        benefitOrderDO.setAdExt(oldOrderDO.getAdExt());
        benefitOrderDO.setPayChannelId(oldOrderDO.getPayChannelId());
        benefitOrderDO.setPayChannel(oldOrderDO.getPayChannel());
        benefitOrderDO.setPayChannelPkgId(oldOrderDO.getPayChannelPkgId());
        benefitOrderDO.setPayChannelPkgName(oldOrderDO.getPayChannelPkgName());
        benefitOrderDO.setPhone(oldOrderDO.getPhone());
        benefitOrderDO.setProdId(oldOrderDO.getProdId());
        benefitOrderDO.setProdName(oldOrderDO.getProdName());
        benefitOrderDO.setRedeemLimit(oldOrderDO.getRedeemLimit());
        benefitOrderDO.setRedeemRemain(oldOrderDO.getRedeemRemain());
        benefitOrderDO.setCycleType(oldOrderDO.getCycleType());
        benefitOrderDO.setCycleRedeemLimit(oldOrderDO.getCycleRedeemLimit());
        benefitOrderDO.setOrderAmount(oldOrderDO.getOrderAmount());
        benefitOrderDO.setPayStatus(Objects.equals(params.getStatus(), "1") ? PayStatusEnum.RENEW.getCode() : PayStatusEnum.UNSUBSCRIBE.getCode());
        benefitOrderDO.setPayNotifyContent(JSON.toJSONString(params));
        benefitOrderDO.setPayNotifyTime(DateUtil.date().toLocalDateTime());
        benefitOrderDO.setOrderStatus(oldOrderDO.getOrderStatus());
        benefitOrderDO.setPreorderStatus(oldOrderDO.getPreorderStatus());
        benefitOrderDO.setOutChannelId(oldOrderDO.getOutChannelId());
        benefitOrderDO.setOutChannelName(oldOrderDO.getOutChannelName());
        benefitOrderDO.setCreateTime(DateUtil.parse(params.getTime(), "yyyyMMddHHmmss"));
        benefitOrderDOService.save(benefitOrderDO);

        // 添加日志
        benefitOrderLogService.orderNotify(benefitOrderDO, JSON.toJSONString(params));

        return BizResult.error("0000", "成功");
    }

    public void sendEventPoint(UnicomWoPayPkgEnum unicomWoPayPkgEnum, EventPointEnum eventType, String mobile, String tid, String orderNo) {
        if (!UnicomWoPayPkgEnum.WX_WO_PAY.equals(unicomWoPayPkgEnum)) {
            return;
        }
        HttpRequest request = this.buildEventPointReq(eventType.getValue(), mobile, orderNo, tid);
        log.info("发送[埋点]请求{}", request);
        HttpResponse execute = request.execute();
        log.info("请求[埋点]状态:{}响应:{}", execute.getStatus(), execute.body());
        if (!execute.isOk()) {
            log.error("请求[埋点]状态:{}", execute.getStatus());
        }
    }

    private HttpRequest buildEventPointReq(Integer eventType, String mobile, String orderNo, String tid) {
        Map<String, Object> param = new HashMap<>();
        param.put("source", 102);
        param.put("key", "LuJr52l@z5276Kc8");
        param.put("type", 1);
        param.put("productid", "11084");
        param.put("event_type", eventType);
        param.put("mobile", mobile);
        if (EventPointEnum.isOrderEvent(eventType)) {
            param.put("order_id", orderNo);
        }
        param.put("channelid", "15796641");
        //当前页面埋点的唯一标识， 32位的uuid
        param.put("treesid", tid);
        //支付方式
        if (EventPointEnum.isOrderEvent(eventType)) {
            param.put("paytype", orderNo);
        } else {
            //微信支付
            param.put("paytype", 8);
        }
        return UnicomReadTrackUtil.request(param);
    }

    @Getter
    @AllArgsConstructor
    public enum EventPointEnum {
        //
        STEP_1(1, "打开页面"),
        STEP_2(2, "点击订购"),
        STEP_3(3, "获取验证码（cap页面）"),
        STEP_4(4, "输入验证码(cap页面)"),
        STEP_5(5, "点击订购确认(cap页面)"),
        STEP_6(6, "订购成功"),
        STEP_7(7, "退出页面"),
        ;

        private final Integer value;
        private final String desc;

        public static Boolean isOrderEvent(Integer value) {
            return STEP_2.value.equals(value) || STEP_6.value.equals(value);
        }
    }

    @Data
    public static class UnicomReadPayNotify {
        private String orderNo;
        private String sign;
        /**
         * 0 申请临时订单成功 --中间态回调里面不会有
         * 1 打开网页 --中间态回调里面不会有
         * 2 用户订购 --中间态回调里面不会有
         * 3 订购成功
         * 4 订购失败
         */
        private String status;
    }


    // public static void main(String[] args) {
    //     final String url = "http://pcc.woread.com.cn/oauth/client/key";
    //     Map<String, Object> paramMap = new HashMap<>();
    //     paramMap.put("clientSource", "11");
    //     paramMap.put("clientId", "1");
    //     paramMap.put("clientSecret", "2");
    //     HttpRequest request = HttpRequest.get(url).form(paramMap).timeout(TIME_OUT);
    //     log.info("发送请求{}", request);
    //     HttpResponse execute = request.execute();
    //     log.info("请求状态:{}响应:{}", execute.getStatus(), execute.body());
    // }


}
