package com.yuelan.hermes.quanyi.remote.benefit;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.YuXinPayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.properties.YuXinProperties;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2025/4/29
 * @since 2025/4/29
 * <p>
 * 豫信支付
 * <p>
 * 文档：<a href="https://www.showdoc.com.cn/2598835231684567/11558501928107049">文档地址</a>
 * 密码：88888888
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class YuXinManager {

    private static final String SMS_REQ_URL = "http://dx.ipv.so/v1/cmcc.ashx/sendSms";
    private static final String SMS_VERIFY_URL = "http://dx.ipv.so/v1/cmcc.ashx/verifySms";

    private final YuXinProperties yuXinProperties;
    private final BenefitOrderLogService benefitOrderLogService;

    /**
     * 发送短信验证码
     *
     * @param yuXinPayPkgEnum 产品包
     * @return SmsCodeResp
     */
    public SmsCodeResp sendSmsCode(String mobile, YuXinPayPkgEnum yuXinPayPkgEnum) {
        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, mobile);

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(SMS_REQ_URL)
                .contentType(ContentType.JSON.getValue())
                .body(buildSmsReqBody(yuXinPayPkgEnum, mobile))
                .log(BenefitPayChannelEnum.YU_XIN, args);
        HttpResponse resp = benefitOrderLogService.http(requestWrapper);
        return JSON.parseObject(resp.body(), SmsCodeResp.class);
    }

    /**
     * 验证验证码
     *
     * @param phone   手机
     * @param smsCode 验证码
     * @param orderNo 我方系统的订单号
     * @param od      获取验证码时候响应的订单号
     */
    public SmsVerifyResp verifySmsCode(String phone, String smsCode, String orderNo, String od, YuXinPayPkgEnum yuXinPayPkgEnum) {
        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_CHECK, phone, orderNo);

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(SMS_VERIFY_URL)
                .contentType(ContentType.JSON.getValue())
                .body(buildSmsVerifyReqBody(yuXinPayPkgEnum, phone, smsCode, od))
                .log(BenefitPayChannelEnum.YU_XIN, args);
        HttpResponse resp = benefitOrderLogService.http(requestWrapper);
        return JSON.parseObject(resp.body(), SmsVerifyResp.class);
    }


    /**
     * 短信验证码请求body
     */
    private String buildSmsReqBody(YuXinPayPkgEnum pkgEnum, String phone) {
        YuXinProperties.ProdConfig prodConfig = yuXinProperties.getProdConfigMap().get(pkgEnum);
        if (pkgEnum.getApiType() == YuXinPayPkgEnum.ApiType.HN_CMCC) {
            return hnCmccSmsReqBody(prodConfig, phone);
        }
        throw BizException.create(BaseErrorCodeEnum.SYS_ERROR, "系统内部异常");
    }

    /**
     * 验证码验证请求
     */
    private String buildSmsVerifyReqBody(YuXinPayPkgEnum pkgEnum, String phone, String verifyCode, String spOrderNo) {
        YuXinProperties.ProdConfig prodConfig = yuXinProperties.getProdConfigMap().get(pkgEnum);
        if (pkgEnum.getApiType() == YuXinPayPkgEnum.ApiType.HN_CMCC) {
            return hnCmccSmsVerifyReqBody(prodConfig, phone, verifyCode, spOrderNo);
        }
        throw BizException.create(BaseErrorCodeEnum.SYS_ERROR, "系统内部异常");
    }


    /**
     * 湖南-短信验证码请求body
     */
    private String hnCmccSmsReqBody(YuXinProperties.ProdConfig prodConfig, String phone) {
        String prodCode = prodConfig.getProdCode();
        String time = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN);
        return new JSONObject() {{
            put("appKey", yuXinProperties.getAppKey());
            put("phone", phone);
            put("product", prodCode);
            put("timestamp", time);
            put("sign", sign(time));
        }}.toJSONString();
    }


    /**
     * 湖南-验证码验证请求
     */
    private String hnCmccSmsVerifyReqBody(YuXinProperties.ProdConfig prodConfig, String phone, String verifyCode, String spOrderNo) {
        String prodCode = prodConfig.getProdCode();
        String time = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN);
        return new JSONObject() {{
            put("appKey", yuXinProperties.getAppKey());
            put("phone", phone);
            put("sms", verifyCode);
            put("product", prodCode);
            put("orderNo", spOrderNo);
            put("timestamp", time);
            put("sign", sign(time));
        }}.toJSONString();
    }


    /**
     * * 签名
     *
     * @param time 时间
     */
    private String sign(String time) {
        // md5(appKey+timestamp+appSecret)
        String appKey = yuXinProperties.getAppKey();
        String appSecret = yuXinProperties.getAppSecret();
        return SecureUtil.md5(appKey + time + appSecret);
    }

    /**
     * 获取短信验证码response
     */
    @Data
    public static class SmsCodeResp {
        /**
         * 状态码  =0 成功
         */
        private Integer code;

        /**
         * 订单号
         */
        private String od;

        private String msg;

        public boolean isSuccess() {
            return code != null && code == 0;
        }


    }

    @Data
    public static class SmsVerifyResp {
        /**
         * 状态码  =0 成功
         */
        private Integer code;

        private String msg;

        public boolean isSuccess() {
            return code != null && code == 0;
        }


    }

}
