package com.yuelan.hermes.quanyi.remote.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p> 特祯 -虚拟产品异步下单</p>
 *
 * <AUTHOR>
 * @date 2024/2/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VProductRechargeReq extends TezhenBaseReq {
    /**
     * 产品类型
     */
    @JSONField(name = "ProductType")
    private String productType;
    /**
     * 充值账号
     */
    @JSONField(name = "Mobile")
    private String mobile;
    /**
     * 包体类型
     */
    @JSONField(name = "Packagesize")
    private String packagesize;
    /**
     * 代理订单号
     * 代理侧提供的唯一订单号,长度：【1~64字符】
     */
    @JSONField(name = "LinkId")
    private String linkId;
    /**
     * 状态回调地址
     */
    @JSONField(name = "Reporturl")
    private String reporturl;


}