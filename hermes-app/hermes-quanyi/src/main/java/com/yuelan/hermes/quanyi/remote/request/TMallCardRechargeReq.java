package com.yuelan.hermes.quanyi.remote.request;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR> 2024/5/13 下午7:54
 * <p>
 * String orderNo, String rechargeAccount, long amount, long parValue
 */
@Data
@AllArgsConstructor
public class TMallCardRechargeReq {
    /**
     * 防重订单号
     * 订单号一样并且其他充值参数一样 返回的是第一次的结果
     */
    private String preventDuplicationOrderNo;
    /**
     * 充值账号
     */
    private String taobaoOpenUid;
    /**
     * 充值金额
     */
    private long amount;
    /**
     * 面值
     */
    private long parValue;
}
