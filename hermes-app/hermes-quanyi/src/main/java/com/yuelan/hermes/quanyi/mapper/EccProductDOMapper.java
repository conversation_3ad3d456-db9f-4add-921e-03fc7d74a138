package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2024/5/2 上午12:03
 */
public interface EccProductDOMapper extends BaseMapper<EccProductDO> {

    default int selectCountByProdCode(String code) {
        return selectCount(Wrappers.<EccProductDO>lambdaQuery()
                .eq(EccProductDO::getProdCode, code)).intValue();
    }

    default EccProductDO selectByProdName(String prodName) {
        return selectOne(Wrappers.<EccProductDO>lambdaQuery()
                .eq(EccProductDO::getProdName, prodName));
    }

    default EccProductDO getByProdCode(String prodCode) {
        return selectOne(Wrappers.<EccProductDO>lambdaQuery()
                .eq(EccProductDO::getProdCode, prodCode));
    }

    /**
     * 分页查询 扩展查询其他信息
     */
    IPage<EccProductDO> selectPageExpand(IPage<EccProductDO> page, @Param(Constants.WRAPPER) Wrapper<EccProductDO> eccProductDOWrapper);

    default List<EccProductDO> selectListBySpProdId(Integer spProdId) {
        return selectList(Wrappers.<EccProductDO>lambdaQuery()
                .eq(EccProductDO::getSpProdId, spProdId));
    }
}