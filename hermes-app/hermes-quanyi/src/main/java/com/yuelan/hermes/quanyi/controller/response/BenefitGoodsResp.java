package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 19:12
 */
@Data
public class BenefitGoodsResp {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long goodsId;

    /**
     * 商品名
     */
    @Schema(description = "商品名")
    private String goodsName;

    /**
     * 供应商类型
     */
    @Schema(description = "供应商类型")
    private Integer supplierType;

    /**
     * 供应商名字
     */
    @Schema(description = "供应商名字")
    private String supplierName;

    /**
     * 供应商商品编号
     */
    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    /**
     * 产品图
     */
    @Schema(description = "产品图")
    private String goodsImg;

    /**
     * 面值
     */
    @Schema(description = "面值")
    private BigDecimal parValue;

    /**
     * 成本价格
     */
    @Schema(description = "成本价格")
    private BigDecimal costPrice;

    /**
     * 售价
     */
    @Schema(description = "售价")
    private BigDecimal price;

    /**
     * 上下架状态：0-下架；1-上架
     */
    @Schema(description = "上下架状态：0-下架；1-上架")
    private Integer goodsStatus;


    public static BenefitGoodsResp buildResp(BenefitGoodsDO benefitGoodsDO) {
        if (Objects.isNull(benefitGoodsDO)) {
            return null;
        }
        BenefitGoodsResp resp = new BenefitGoodsResp();
        resp.setGoodsId(benefitGoodsDO.getGoodsId());
        resp.setGoodsName(benefitGoodsDO.getGoodsName());
        resp.setSupplierType(benefitGoodsDO.getSupplierType());
        resp.setSupplierName(benefitGoodsDO.getSupplierName());
        resp.setSupplierGoodsNo(benefitGoodsDO.getSupplierGoodsNo());
        resp.setGoodsImg(benefitGoodsDO.getGoodsImg());
        resp.setCostPrice(benefitGoodsDO.getCostPrice());
        resp.setParValue(benefitGoodsDO.getParValue());
        resp.setPrice(benefitGoodsDO.getPrice());
        resp.setGoodsStatus(benefitGoodsDO.getGoodsStatus());
        return resp;
    }


}
