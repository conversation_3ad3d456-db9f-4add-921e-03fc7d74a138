package com.yuelan.hermes.quanyi.remote.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2025/6/13
 * @since 2025/6/13
 */
@Data
public class SkuPageSaveOrUpdateReq {

    @Schema(description = "权益平台的productId")
    private Long productId;

    @Schema(description = "pageId不能为空")
    private Long pageId;

    @Schema(description = "落地页名称")
    private String pageName;

    @Schema(description = "首屏需要传 完整路径")
    private String indexImg;

}
