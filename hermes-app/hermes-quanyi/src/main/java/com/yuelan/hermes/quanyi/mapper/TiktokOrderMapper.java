package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.TiktokOrderDO;

/**
 * <AUTHOR> 2024/9/7 15:30
 */
public interface TiktokOrderMapper extends BaseMapper<TiktokOrderDO> {

    default TiktokOrderDO baseGetOne(TiktokOrderDO queryDO) {
        return selectOne(new QueryWrapper<>(TiktokOrderDO.class)
                .setEntity(queryDO));
    }
}