package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO;
import com.yuelan.hermes.quanyi.controller.request.GamingOrderItemListReq;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

public interface GamingOrderItemMapper extends BaseMapper<GamingOrderItemDO> {
    int batchInsert(@Param("list") List<GamingOrderItemDO> list);

    GamingOrderItemDO findByItemNo(@Param("itemNo") String itemNo);

    List<Long> findOrderIdBySupplierOrderNo(@Param("supplierOrderNo") String supplierOrderNo);

    List<GamingOrderItemDO> findByOrderId(@Param("orderId") Long orderId);

    List<GamingOrderItemDO> findByOrderIdIn(@Param("orderIds") Collection<Long> orderIds);

    int updateStatus(@Param("itemId") Long itemId, @Param("orgStatus") Integer orgStatus,
                     @Param("tarStatus") Integer tarStatus, @Param("remark") String remark);

    Long existByOrderIdAndStatus(@Param("orderId") Long orderId, @Param("status") Collection<Integer> status);

    List<Integer> findStatusByOrderIdAndStatus(@Param("orderId") Long orderId, @Param("status") Collection<Integer> status);

    List<Integer> findStatusByOrderId(@Param("orderId") Long orderId);

    int updateSupplierOrder(@Param("itemId") Long itemId, @Param("supplierOrderNo") String supplierOrderNo, @Param("oldStatus") Integer oldStatus, @Param("orderStatus") Integer orderStatus,
                            @Param("preorderStatus") Integer preorderStatus, @Param("preorderRemark") String preorderRemark);


    default List<GamingOrderItemDO> listByOrderStatus(Integer orderStatus, Long limit) {
        return selectList(Wrappers.<GamingOrderItemDO>lambdaQuery()
                .eq(GamingOrderItemDO::getOrderStatus, orderStatus).last(Objects.nonNull(limit), "limit " + limit));
    }

    default List<GamingOrderItemDO> listByObtainStatus(Integer obtainStatus, Long limit) {
        return selectList(Wrappers.<GamingOrderItemDO>lambdaQuery()
                .eq(GamingOrderItemDO::getObtainStatus, obtainStatus).last(Objects.nonNull(limit), "limit " + limit));
    }

    default List<GamingOrderItemDO> listByPhoneAndOrderStatusStatusAndProductIdAndGoodsId(String phone, Integer orderStatus, Long productId, Long goodsId) {
        return selectList(Wrappers.<GamingOrderItemDO>lambdaQuery()
                .eq(GamingOrderItemDO::getPhone, phone)
                .eq(GamingOrderItemDO::getOrderStatus, orderStatus)
                .eq(GamingOrderItemDO::getProductId, productId)
                .eq(GamingOrderItemDO::getGoodsId, goodsId));
    }

    default List<GamingOrderItemDO> listByPreorderStatus(Integer preorderStatus, Long limit) {
        return selectList(Wrappers.<GamingOrderItemDO>lambdaQuery()
                .eq(GamingOrderItemDO::getPreorderStatus, preorderStatus)
                .last(Objects.nonNull(limit), "limit " + limit));
    }

    default Long findOrderItemMaxId() {
        return selectOne(Wrappers.<GamingOrderItemDO>lambdaQuery()
                .select(GamingOrderItemDO::getId)
                .orderByDesc(GamingOrderItemDO::getId).last("limit 1"))
                .getId();
    }

    default Integer findOrderItemCountByIdRangAndOrderStatus(Long startId, Long endId, Integer orderStatus) {
        return selectCount(Wrappers.<GamingOrderItemDO>lambdaQuery()
                .ge(GamingOrderItemDO::getId, startId)
                .le(GamingOrderItemDO::getId, endId)
                .eq(GamingOrderItemDO::getOrderStatus, orderStatus)
                .notLike(GamingOrderItemDO::getPreorderRemark, "order exist")
        ).intValue();
    }

    Long countByReq(@Param("req") GamingOrderItemListReq req);

    List<GamingOrderItemDO> listByReq(@Param("req") GamingOrderItemListReq req);

    default List<GamingOrderItemDO> findByPhoneAndTime(String phone, LocalDateTime start, LocalDateTime end) {
        return selectList(Wrappers.<GamingOrderItemDO>lambdaQuery()
                .eq(GamingOrderItemDO::getPhone, phone)
                .ge(GamingOrderItemDO::getCreateTime, start)
                .le(GamingOrderItemDO::getCreateTime, end));
    }

    /**
     * 根据手机号、时间范围和供应商类型查询订单明细
     *
     * @param phone        手机号
     * @param start        开始时间
     * @param end          结束时间
     * @param supplierType 供应商类型
     * @return 订单明细列表
     */
    default List<GamingOrderItemDO> findByPhoneAndTimeAndSupplierType(String phone, LocalDateTime start, LocalDateTime end, Integer supplierType) {
        return selectList(Wrappers.<GamingOrderItemDO>lambdaQuery()
                .eq(GamingOrderItemDO::getPhone, phone)
                .ge(GamingOrderItemDO::getCreateTime, start)
                .le(GamingOrderItemDO::getCreateTime, end)
                .eq(GamingOrderItemDO::getSupplierType, supplierType));
    }
}