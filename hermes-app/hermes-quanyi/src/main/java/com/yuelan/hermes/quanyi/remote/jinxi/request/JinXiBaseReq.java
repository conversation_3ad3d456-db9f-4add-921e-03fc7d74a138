package com.yuelan.hermes.quanyi.remote.jinxi.request;

import lombok.Data;

/**
 * 今溪基础请求类
 * 包含所有接口的通用参数
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Data
public abstract class JinXiBaseReq {

    /**
     * 商户Key，平台分配给商户的app_key
     */
    private String app_key;

    /**
     * 时间戳，格式为：yyyy-MM-dd HH:mm:ss
     */
    private String timestamp;

    /**
     * 调用的接口版本，该值固定为2.0
     */
    private String version = "2.0";

    /**
     * 签名加密类型，该值固定为md5
     */
    private String sign_type = "md5";

    /**
     * 签名串，签名规则详见示例
     */
    private String sign;
}
