package com.yuelan.hermes.quanyi.remote.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * <p> 特祯 -虚拟产品异步下单</p>
 *
 * <AUTHOR>
 * @date 2024/2/21
 */
@Data
public class TezhenBaseReq {
    /**
     * 代理账号
     */
    @JSONField(name = "Account")
    private String account;
    /**
     * 签名
     * 参数名小写，参数值不转换
     * MD5(account=account&timestamp=timestamp&key=key)
     */
    @JSONField(name = "Sign")
    private String sign;
    /**
     * 当前时间减去 1970-01-01 00.00.00 得到的毫秒数
     */
    @JSONField(name = "Timestamp")
    private String timestamp;


}