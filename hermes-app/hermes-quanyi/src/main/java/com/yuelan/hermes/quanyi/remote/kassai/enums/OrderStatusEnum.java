package com.yuelan.hermes.quanyi.remote.kassai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 卡赛订单状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/09
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    /**
     * 初始状态
     */
    INITIAL(100, "初始状态"),

    /**
     * 待支付
     */
    WAIT_PAY(200, "待支付"),

    /**
     * 支付中
     */
    PAYING(201, "支付中"),

    /**
     * 支付成功
     */
    PAY_SUCCESS(202, "支付成功"),

    /**
     * 支付失败
     */
    PAY_FAILED(203, "支付失败"),

    /**
     * 待订购
     */
    WAIT_ORDER(300, "待订购"),

    /**
     * 订购中
     */
    ORDERING(301, "订购中"),

    /**
     * 订购成功
     */
    ORDER_SUCCESS(302, "订购成功"),

    /**
     * 订购失败
     */
    ORDER_FAILED(303, "订购失败"),

    /**
     * 订购待重试
     */
    ORDER_RETRY(304, "订购待重试"),

    /**
     * 待商户确认
     */
    WAIT_MERCHANT_CONFIRM(1000, "待商户确认"),

    /**
     * 预处理成功
     */
    PREPROCESS_SUCCESS(1005, "预处理成功"),

    /**
     * 预处理失败
     */
    PREPROCESS_FAILED(1010, "预处理失败"),

    /**
     * 备货中
     */
    STOCKING(1015, "备货中"),

    /**
     * 已发货
     */
    SHIPPED(1020, "已发货"),

    /**
     * 订购完成
     */
    ORDER_COMPLETED(1025, "订购完成"),

    /**
     * 订购失败（终态）
     */
    ORDER_FINAL_FAILED(1030, "订购失败"),

    /**
     * 已激活（终态）
     */
    ACTIVATED(1035, "已激活"),

    /**
     * 激活失败
     */
    ACTIVATE_FAILED(1040, "激活失败"),

    /**
     * T月充值
     */
    CURRENT_MONTH_RECHARGE(1055, "T月充值"),

    /**
     * OAO充值
     */
    OAO_RECHARGE(1065, "OAO充值");

    private final Integer code;
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static OrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为终态状态
     *
     * @return true表示终态
     */
    public boolean isFinalStatus() {
        return this == ORDER_FAILED || this == ORDER_FINAL_FAILED || this == ACTIVATED;
    }
}
