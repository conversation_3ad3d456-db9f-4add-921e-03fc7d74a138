package com.yuelan.hermes.quanyi.remote.softgame.response;

import lombok.Data;

/**
 * 软游通v2基础响应格式
 *
 * <AUTHOR> 2025/7/31
 * @since 2025/7/31
 */
@Data
public class SoftGameBaseResponse<T> {

    /**
     * 返回编码
     * 01 - 下单成功，仅代表下单成功，不代表充值成功
     * 02 - 下单失败
     * 03 - 订单号重复，校验周期为近30日内，建议走人工核查
     * 04 - 已达到风控限制，佳之易平台设置的风控限制
     */
    private String result;

    /**
     * 下单结果描述
     */
    private String mes;

    /**
     * 数据内容
     */
    private T data;

    /**
     * 创建成功响应
     */
    public static <T> SoftGameBaseResponse<T> success(T data) {
        SoftGameBaseResponse<T> response = new SoftGameBaseResponse<>();
        response.setResult("01");
        response.setMes("成功");
        response.setData(data);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static <T> SoftGameBaseResponse<T> fail(String result, String message) {
        SoftGameBaseResponse<T> response = new SoftGameBaseResponse<>();
        response.setResult(result);
        response.setMes(message);
        return response;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return "01".equals(result);
    }

    public boolean isRealTimeSuccess() {
        return false;
    }
}
