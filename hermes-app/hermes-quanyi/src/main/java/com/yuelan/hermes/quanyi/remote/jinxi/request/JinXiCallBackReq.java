package com.yuelan.hermes.quanyi.remote.jinxi.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;


/**
 * 今溪回调请求
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Data
public class JinXiCallBackReq {

    /**
     * 平台订单号
     */
    private String orderId;

    /**
     * 客户订单号
     */
    private String customOrderId;

    /**
     * 订单状态：10. 待充值 20. 充值中 30. 充值成功 40.充值失败 50.可疑
     */
    private String orderStatus;

    /**
     * 签名串
     */
    private String sign;

    /**
     * 判断订单是否成功
     */
    @JSONField(serialize = false)
    public boolean isSuccess() {
        return "30".equals(orderStatus);
    }

    /**
     * 判断订单是否失败
     */
    @JSONField(serialize = false)
    public boolean isFailed() {
        return "40".equals(orderStatus);
    }

    /**
     * 判断订单是否处理中
     */
    @JSONField(serialize = false)
    public boolean isProcessing() {
        return "20".equals(orderStatus);
    }

    /**
     * 判断是否为最终状态
     */
    @JSONField(serialize = false)
    public boolean isFinalStatus() {
        return isSuccess() || isFailed();
    }
}
