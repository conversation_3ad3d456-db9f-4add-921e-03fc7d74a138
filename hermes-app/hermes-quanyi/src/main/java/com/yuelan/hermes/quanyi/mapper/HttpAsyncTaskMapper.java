package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.yuelan.hermes.quanyi.common.enums.TaskStatusEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.HttpAsyncTask;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 2025/3/21
 * @since 2025/3/21
 */
public interface HttpAsyncTaskMapper extends BaseMapper<HttpAsyncTask> {

    /**
     * 根据requestId查询任务
     */
    default HttpAsyncTask selectByRequestId(String requestId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(HttpAsyncTask::getRequestId, requestId)
                .one();
    }

    /**
     * 查询待处理的任务
     */
    default List<HttpAsyncTask> selectPendingTasks(@Param("limit") int limit) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(HttpAsyncTask::getStatus, TaskStatusEnum.PENDING.getCode())
                .orderByAsc(HttpAsyncTask::getCreateTime)
                .last("limit " + limit)
                .list();
    }

    /**
     * 查询需要重试的任务
     * 执行失败 并且下次重试时间小于等于当前时间
     */
    default List<HttpAsyncTask> selectTasksForRetry(@Param("currentTime") LocalDateTime currentTime, @Param("limit") int limit) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(HttpAsyncTask::getStatus, TaskStatusEnum.FAILED.getCode())
                .le(HttpAsyncTask::getNextRetryTime, currentTime)
                .orderByAsc(HttpAsyncTask::getNextRetryTime)
                .last("limit " + limit)
                .list();
    }

    /**
     * 更新任务状态
     */
    default boolean updateStatus(@Param("id") Long id, @Param("status") Integer status,
                                 @Param("lastResponse") String lastResponse,
                                 @Param("lastResponseCode") Integer lastResponseCode) {
        return new LambdaUpdateChainWrapper<>(this)
                .eq(HttpAsyncTask::getId, id)
                .set(HttpAsyncTask::getStatus, status)
                .set(HttpAsyncTask::getLastResponse, lastResponse)
                .set(HttpAsyncTask::getLastResponseCode, lastResponseCode)
                .update();
    }

    /**
     * 更新重试信息
     */
    default boolean updateRetryInfo(@Param("id") Long id, @Param("retryCount") Integer retryCount,
                                    @Param("nextRetryTime") LocalDateTime nextRetryTime) {
        return new LambdaUpdateChainWrapper<>(this)
                .eq(HttpAsyncTask::getId, id)
                .set(HttpAsyncTask::getRetryCount, retryCount)
                .set(HttpAsyncTask::getNextRetryTime, nextRetryTime)
                .update();
    }

    /**
     * 根据业务类型和业务ID查询任务
     */
    default List<HttpAsyncTask> selectByBusiness(@Param("businessType") String businessType,
                                                 @Param("businessId") String businessId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(HttpAsyncTask::getBusinessType, businessType)
                .eq(HttpAsyncTask::getBusinessId, businessId)
                .list();
    }

}