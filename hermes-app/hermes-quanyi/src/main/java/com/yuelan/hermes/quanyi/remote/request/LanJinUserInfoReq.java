package com.yuelan.hermes.quanyi.remote.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 蓝鲸电竞卡 - 查看用户信息请求
 *
 * <AUTHOR> Generated
 * @since 2025-07-10
 */
@Data
public class LanJinUserInfoReq {

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    @NotBlank(message = "用户账号不能为空")
    private String account;

    /**
     * 渠道标识
     */
    @Schema(description = "渠道标识")
    @NotBlank(message = "渠道标识不能为空")
    private String channel;

    /**
     * 时间戳（秒为单位，10分钟过期）
     */
    @Schema(description = "时间戳（秒为单位，10分钟过期）")
    @NotBlank(message = "时间戳不能为空")
    private String stamp;

    /**
     * 签名
     */
    @Schema(description = "签名")
    @NotBlank(message = "签名不能为空")
    private String sign;
}
