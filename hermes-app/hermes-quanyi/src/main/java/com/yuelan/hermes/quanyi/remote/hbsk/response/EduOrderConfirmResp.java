package com.yuelan.hermes.quanyi.remote.hbsk.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 教育包订单确认响应
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class EduOrderConfirmResp {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String text;

    /**
     * 扩展字段（TW值）
     */
    private String ext;

    /**
     * 判断是否成功
     */
    @JSONField(serialize = false)
    public boolean isSuccess() {
        return code != null && code == 200;
    }


}
