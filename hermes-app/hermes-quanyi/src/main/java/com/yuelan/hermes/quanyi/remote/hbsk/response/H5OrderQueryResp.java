package com.yuelan.hermes.quanyi.remote.hbsk.response;

import lombok.Data;

/**
 * H5计费订单查询响应
 *
 * <AUTHOR> 2025/7/17
 * @since 2025/7/17
 */
@Data
public class H5OrderQueryResp {

    /**
     * 执行结果代码 0-成功 96-订单不存在 97-参数不能为空 99-失败
     */
    private Integer code;

    /**
     * 执行结果描述
     */
    private String message;

    /**
     * 订单号
     */
    private String order_no;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 校验类型（0：免密登录 1：短信验证码）
     */
    private Integer verify_type;

    /**
     * 产品ID
     */
    private String product_id;

    /**
     * 产品名称
     */
    private String product_name;

    /**
     * 下单时间
     */
    private String order_time;

    /**
     * 订单状态（1-新建；2-确认成功；3-确认失败；4-开户在途；5-开户失败；6-延时体验状态；7-补开户中）
     */
    private Integer order_status;

    /**
     * 产品价格（单位为分）
     */
    private String price;

    /**
     * 合作方返回地址
     */
    private String return_url;

    /**
     * 是否开户（0-否（默认），1-是）
     */
    private Integer is_openaccount;

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }
}
