package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class KnhqSubmitOrderReq extends KnhqBaseReq {
    /**
     * 充值号码
     */
    private String mobile;
    /**
     * 商户订单号，格式满足：
     * ^[0-9a-zA-Z][0-9a-zA-Z_-]{3,40}$
     */
    private String merchantOrderId;
    /**
     * 充值面值，10-500
     */
    private int faceAmount;
    /**
     * 回掉地址
     */
    private String callbackUrl;
    /**
     * 产品类型，1=话费快充
     */
    private int priceType = 1;
    /**
     * 省份代码
     */
    private int province = 0;
    /**
     * 运营商代码，1移动/2联通/3电信
     */
    private int sp = 0;

}
