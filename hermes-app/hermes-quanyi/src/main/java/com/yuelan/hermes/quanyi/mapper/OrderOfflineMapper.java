package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.OrderOfflineDO;
import com.yuelan.hermes.quanyi.controller.request.OrderOfflineListReq;
import com.yuelan.hermes.quanyi.controller.response.OrderOfflineRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderOfflineMapper extends BaseMapper<OrderOfflineDO> {

    Long countByOrderOfflineListReq(@Param("req") OrderOfflineListReq req);

    List<OrderOfflineRsp> pageByOrderOfflineListReq(@Param("req") OrderOfflineListReq req);

    List<String> findBuyerDistinct();

    OrderOfflineDO findByOrderNo(@Param("orderNo") String orderNo);

    int updateOrderStatus(@Param("orderNo") String orderNo, @Param("status") int status);
}