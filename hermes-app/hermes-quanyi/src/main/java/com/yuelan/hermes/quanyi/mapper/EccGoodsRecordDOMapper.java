package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsRecordDO;

import java.util.List;

/**
 * <AUTHOR> 2024/5/15 下午5:33
 */
public interface EccGoodsRecordDOMapper extends BaseMapper<EccGoodsRecordDO> {
    default List<EccGoodsRecordDO> listByPhoneAndProdIdAndMonth(String phone, Long prodId, String month) {
        return selectList(Wrappers.<EccGoodsRecordDO>lambdaQuery()
                .eq(EccGoodsRecordDO::getPhone, phone)
                .eq(EccGoodsRecordDO::getProdId, prodId)
                .eq(EccGoodsRecordDO::getEccOrderMonth, month));
    }
}