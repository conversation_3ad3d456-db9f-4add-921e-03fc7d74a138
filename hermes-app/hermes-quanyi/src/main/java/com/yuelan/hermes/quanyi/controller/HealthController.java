package com.yuelan.hermes.quanyi.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.yuelan.boot.utils.HttpServletRequestUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Tag(name = "健康检查")
@Slf4j
@RestController
public class HealthController {
    // 安全下线 ip
    private static final String safeIp = "127.0.0.1";
    private static volatile boolean shutdown = false;
    private static volatile boolean RETURN_SHUTDOWN = false;
    private static CountDownLatch shutdownLatch = null;

    @GetMapping(value = "/")
    public String isAvailable() {
        return "ok";
    }

    @GetMapping(value = "/monitor/shutdown")
    public String shutdown() {
        String clientIP = ServletUtil.getClientIP(HttpServletRequestUtil.getCurrentRequest());
        if (!safeIp.equals(clientIP)) {
            return "forbidden";
        }

        log.info("收到关闭请求，开始优雅关闭流程，客户端IP: {}", clientIP);

        // 重置状态并创建新的CountDownLatch
        synchronized (HealthController.class) {
            shutdown = true;
            RETURN_SHUTDOWN = false;
            shutdownLatch = new CountDownLatch(1);
        }

        try {
            // 等待监控检测调用，最多等待30秒
            long startTime = System.currentTimeMillis();
            boolean detected = shutdownLatch.await(10, TimeUnit.SECONDS);
            long elapsedTime = System.currentTimeMillis() - startTime;

            if (detected && RETURN_SHUTDOWN) {
                log.info("监控已检测到关闭状态，优雅关闭完成，耗时: {}ms", elapsedTime);
                return "shutdown_confirmed";
            } else {
                log.warn("等待监控检测超时，强制返回关闭状态，耗时: {}ms", elapsedTime);
                return "shutdown_timeout";
            }
        } catch (InterruptedException e) {
            log.warn("等待监控检测时被中断", e);
            Thread.currentThread().interrupt();
            return "shutdown_interrupted";
        }
    }

    @GetMapping(value = "/monitor/check")
    public void check(HttpServletResponse response) throws IOException {
        if (shutdown) {
            synchronized (HealthController.class) {
                RETURN_SHUTDOWN = true;
                // 通知shutdown方法可以返回了
                if (shutdownLatch != null) {
                    shutdownLatch.countDown();
                    log.info("监控检测到关闭状态，通知shutdown方法返回");
                }
            }
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("fail");
        } else {
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().write("ok");
        }
    }


}
