package com.yuelan.hermes.quanyi.remote.request;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/11/14
 * @since 2024/11/14
 */
@Data
public class OrderSyncReq {

    @Schema(description = "渠道订单号（唯一）")
    private String channelOrderNo;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "权益平台产品ID")
    private Long productId;

    @Schema(description = "订单时间")
    private LocalDateTime orderTime;

    @Schema(description = "领卡是否成功：1-成功，0-失败")
    private Integer success;

    @Schema(description = "失败原因")
    private String failReason;

    @Schema(description = "按我们平台要求传的不同参数")
    private JSONObject adExtra;
}
