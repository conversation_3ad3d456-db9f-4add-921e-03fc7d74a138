package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RytSendOrderReq extends RytBaseReq {

    /**
     * 商户id，系统分配
     */
    private String businessCode;
    /**
     * 商户订单，商户自定义
     */
    private String businessOrderId;
    /**
     * 回调请求
     */
    private String callBackUrl;
    /**
     * 充值账号
     */
    private String chargeCode;
    /**
     * 商品编码
     */
    private String goodsCode;
    /**
     * 商品数量
     */
    private Integer num;
    /**
     * 充值的ip
     */
    private String tradeIp;


}
