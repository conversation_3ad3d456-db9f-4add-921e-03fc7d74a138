package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.service.SoftGameService;
import com.yuelan.hermes.quanyi.remote.softgame.request.SoftGameNotifyReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 软游通v2回调接口
 *
 * <AUTHOR> 2025/7/31
 * @since 2025/7/31
 */
@Slf4j
@Tag(name = "软游通v2 API")
@RequestMapping("/softgame")
@RestController
@SaIgnore
public class SoftGameController {

    @Autowired
    private SoftGameService softGameService;

    /**
     * 异步结果通知回调
     *
     * @param request 通知请求
     * @return 固定返回 <receive>ok</receive>
     */
    @Operation(summary = "异步结果通知回调")
    @RequestMapping(value = "/notify", method = {RequestMethod.GET, RequestMethod.POST})
    public String notify(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        log.info("软游通v2异步通知回调原始参数: {}", JSON.toJSONString(parameterMap));

        try {
            Map<String, String> params = new HashMap<>();
            parameterMap.forEach((k, v) -> params.put(k, v[0]));
            SoftGameNotifyReq req = JSON.parseObject(JSON.toJSONString(params), SoftGameNotifyReq.class);
            log.info("软游通v2异步通知回调请求: {}", JSON.toJSONString(req));
            boolean result = softGameService.handleNotify(req);
            if (result) {
                log.info("软游通v2异步通知处理成功: {}", req.getUserOrderId());
                return "<receive>ok</receive>";
            } else {
                log.error("软游通v2异步通知处理失败: {}", req.getUserOrderId());
            }
        } catch (Exception e) {
            log.error("软游通v2异步通知处理异常", e);
        }
        // 必须返回 ok 对方要求
        return "<receive>ok</receive>";
    }
}
