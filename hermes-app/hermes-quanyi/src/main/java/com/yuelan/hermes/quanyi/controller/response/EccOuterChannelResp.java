package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/17 下午5:53
 */
@Data
public class EccOuterChannelResp {

    /**
     * 外部渠道id
     */
    @Schema(description = "外部渠道id")
    private Long outerChannelId;


    /**
     * 渠道名字
     */
    @Schema(description = "渠道名字")
    private String channelName;

    /**
     * 后台生成给渠道的参数
     */
    @Schema(description = "apiKey")
    private String apiKey;

    /**
     * 后台生成的密钥
     */
    @Schema(description = "apiSecret")
    private String apiSecret;

    /**
     * 是否禁用：0-表示正常，1-表示禁用
     */
    @Schema(description = "是否禁用：0-表示正常，1-表示禁用")
    private Integer isDisabled;

    /**
     * ip白名单
     */
    @Schema(description = "ip白名单")
    private String ipWhitelist;


    /**
     * 联通zop发展人编号
     */
    @Schema(description = "联通zop发展人编号")
    private String zopReferrerCode;



    public static EccOuterChannelResp buildResp(EccOuterChannelDO channelDO) {
        if (Objects.isNull(channelDO)) {
            return null;
        }
        EccOuterChannelResp resp = new EccOuterChannelResp();
        resp.setOuterChannelId(channelDO.getOuterChannelId());
        resp.setChannelName(channelDO.getChannelName());
        resp.setApiKey(channelDO.getApiKey());
        resp.setApiSecret(channelDO.getApiSecret());
        resp.setIsDisabled(channelDO.getIsDisabled());
        resp.setIpWhitelist(channelDO.getIpWhitelist());
        resp.setZopReferrerCode(channelDO.getZopReferrerCode());
        return resp;
    }
}
