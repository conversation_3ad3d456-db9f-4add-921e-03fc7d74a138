package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2024/4/2 16:17
 */
public interface BenefitProductItemMapper extends BaseMapper<BenefitProductItemDO> {

    default Integer selectCountByGoodsId(Long goodsId) {
        return selectCount(Wrappers.lambdaQuery(BenefitProductItemDO.class)
                .eq(BenefitProductItemDO::getGoodsId, goodsId)).intValue();
    }

    default List<BenefitProductItemDO> listByProdIdAndSort(Long prodId) {
        return selectList(Wrappers.lambdaQuery(BenefitProductItemDO.class)
                .eq(BenefitProductItemDO::getProdId, prodId)
                .orderByAsc(BenefitProductItemDO::getSort)
        );
    }

    default BenefitProductItemDO selectByProdIdAndGoodsId(Long prodId, Long goodsId) {
        return selectOne(Wrappers.lambdaQuery(BenefitProductItemDO.class)
                .eq(BenefitProductItemDO::getProdId, prodId)
                .eq(BenefitProductItemDO::getGoodsId, goodsId));
    }

    default void updateSortById(Long sortedItemId, int sort) {
        update(Wrappers.lambdaUpdate(BenefitProductItemDO.class)
                .eq(BenefitProductItemDO::getProdItemId, sortedItemId)
                .set(BenefitProductItemDO::getSort, sort));
    }

    void reductionSortByDeleteSort(@Param("prodId") Long prodId, @Param("deletedSort") Integer deletedSort);
}