package com.yuelan.hermes.quanyi.remote.jinxi.response;

import lombok.Data;


/**
 * 今溪订单查询响应
 *
 * <AUTHOR> 2025/8/2
 * @since 2025/8/2
 */
@Data
public class JinXiOrderQueryResp {

    /**
     * 购买数量
     */
    private Integer buyNumber;

    /**
     * 卡密信息，JSON格式的数组字符串，"卡密"订单时返回
     */
    private CardInfo cards;

    /**
     * 充值描述
     */
    private String chargeDescription;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 客户订单号，每次请求必须唯一
     */
    private String customOrderId;

    /**
     * 透传参数，下单时提交的透传参数，会原封不动返还
     */
    private String external;

    /**
     * 完成时间
     */
    private String finishTime;

    /**
     * 号卡状态，JSON格式的数组字符串，"号卡"订单时返回
     */
    private NCardInfo nCard;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 订单状态，10.待订购，20. 订购中，30. 订购成功，40. 订购失败，50.订单可疑
     */
    private Integer orderStatus;

    /**
     * 订单类型，10. 直冲订单 20. 卡密订单 30. 话费订单 40. 流量订单 50. 号卡订单
     */
    private Integer orderType;

    /**
     * 商品编号
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 2024/11/15-新增：订购地址，部分商品会返回订购地址，客户需要将订购地址展示给消费者完成订购动作
     */
    private String purchaseUrl;

    /**
     * 充值账号，充值账号或手机号，"卡密"订单时返回空
     */
    private String rechargeAccount;

    /**
     * 投诉退订JSON数据集，注意：当未产生投诉和退订时，该字段为null
     * unsubscribestatus：退订状态（20=已退订，其他=未退订）
     * unsubscribetime：退订时间
     * complaintstatus：投诉状态（20=已投诉，其他=未投诉）
     * complainttime：投诉时间
     */
    private String unsubscribeJson;

    /**
     * 判断订单是否成功
     */
    public boolean isSuccess() {
        return Integer.valueOf(30).equals(orderStatus);
    }

    /**
     * 判断订单是否失败
     */
    public boolean isFailed() {
        return Integer.valueOf(40).equals(orderStatus);
    }

    /**
     * 判断订单是否处理中
     */
    public boolean isProcessing() {
        return Integer.valueOf(20).equals(orderStatus);
    }

    /**
     * 判断是否为最终状态
     */
    public boolean isFinalStatus() {
        return isSuccess() || isFailed();
    }

    /**
     * 卡密数据模型
     */
    @Data
    public static class CardInfo {
        /**
         * 有效期
         */
        private String CardDeadline;

        /**
         * 卡号
         */
        private String CardNumber;

        /**
         * 密码
         */
        private String CardPwd;

        /**
         * 卡类型，10.普通卡密
         */
        private Integer CardType;
    }

    /**
     * 号卡激活数据模型
     */
    @Data
    public static class NCardInfo {
        /**
         * 激活状态，0.未激活 1.已激活
         */
        private Integer activateStatus;

        /**
         * 激活时间
         */
        private String activateTime;

        /**
         * 首充金额
         */
        private Integer firstChargeMoney;

        /**
         * 首冲状态， 0.未首冲 1. 已首冲
         */
        private Integer firstChargeStatus;

        /**
         * 首充时间
         */
        private String firstChargeTime;

        /**
         * 物流公司名称
         */
        private String trackingName;

        /**
         * 物流单号
         */
        private String trackingNumber;

        /**
         * 判断是否已激活
         */
        public boolean isActivated() {
            return Integer.valueOf(1).equals(activateStatus);
        }

        /**
         * 判断是否已首冲
         */
        public boolean isFirstCharged() {
            return Integer.valueOf(1).equals(firstChargeStatus);
        }
    }
}
