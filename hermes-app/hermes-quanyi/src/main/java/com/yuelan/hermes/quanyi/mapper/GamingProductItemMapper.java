package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.bo.GamingProductItemBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GamingProductItemMapper extends BaseMapper<GamingProductItemDO> {
    int batchInsert(@Param("list") List<GamingProductItemDO> list);

    Long existByGoodsId(@Param("goodsId") Long goodsId);

    int removeByProductId(@Param("productId") Long productId);

    int removeById(@Param("id") Long id);

    GamingProductItemDO findById(@Param("id") Long id);

    List<GamingProductItemBO> findByProductId(@Param("productId") Long productId);

    List<GamingProductItemDO> listByProductId(@Param("productId") Long productId);

    GamingProductItemDO findByProductIdAndGoodsId(@Param("productId") Long productId, @Param("goodsId") Long goodsId);
}