package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;

/**
 * <AUTHOR> 2025/6/4
 * @since 2025/6/4
 */
@Data
public class XieYunGdSearchPhoneReq {
    /**
     * regionId 归属地市 市级编码  (必传字段)
     */
    private String regionId;

    /**
     * numFuzzy
     * 手机号模糊匹配 格式：192********192 号段固定，后面的*表 示 模 糊 匹配。*的取值为 0-9。
     */
    private String numFuzzy;

    /**
     * fuzzyTag 0-全模糊，1- 号 头 查询 ， 2- 尾号 查 询 , 3尾号不等于查询
     */
    private String fuzzyTag;

    /**
     * fuzzyKey 模糊匹配关键字 FUZZY_TAG
     * 不为空时取模糊关键字进行查询（ 至 少3位，同时不能为192）
     */
    private String fuzzyKey;

    /**
     * currentPage
     * 当前页 当前页（默认 第 1 页）
     */
    private String currentPage;

    /**
     * pageSize一页显示条数，最大 30（默 认 30条）
     */
    private String pageSize;

    public XieYunGdSearchPhoneReq(String regionId) {
        this.regionId = regionId;
    }
}
