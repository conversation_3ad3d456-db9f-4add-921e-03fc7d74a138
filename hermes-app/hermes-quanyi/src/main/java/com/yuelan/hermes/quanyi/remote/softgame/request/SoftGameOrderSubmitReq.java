package com.yuelan.hermes.quanyi.remote.softgame.request;

import lombok.Data;

/**
 * 软游通v2订单提交请求
 *
 * <AUTHOR> 2025/7/31
 * @since 2025/7/31
 */
@Data
public class SoftGameOrderSubmitReq {

    /**
     * 商户号
     */
    private String businessId;

    /**
     * 订单号 - 由商户自定义，最大长度不超过32位的唯一流水号
     */
    private String userOrderId;

    /**
     * 商品编号 - SUP系统商品编号，通过商务人员获取
     */
    private String goodsId;

    /**
     * 充值账号 - 具体充值用户名或游戏通行证账号
     */
    private String userName;

    /**
     * 商品名称 - 待充值的商品名称，内容不做要求
     */
    private String gameName;

    /**
     * 游戏账号 - 选填，游戏类商品选填
     */
    private String gameAcct;

    /**
     * 游戏区 - 选填，涉及区服的游戏类商品必填
     */
    private String gameArea;

    /**
     * 游戏服 - 选填，涉及区服的游戏类必填
     */
    private String gameSrv;

    /**
     * 充值类型 - 商品类型，如月卡、季卡、年卡
     */
    private String gameType;

    /**
     * 账号类型 - 即充值帐号所属类型
     * 传中文 -手机号
     */
    private String acctType;

    /**
     * 商品面值 - 充值总面值，单位是元，注意是商品面值，非笔数
     */
    private String goodsNum;

    /**
     * 充值ip - 非必填，实际充值用户请求ip，涉及区域匹配的产品必填
     */
    private String orderIp;

    /**
     * 异步通知地址 - 该url地址不能带任何参数，若无需通知充值结果，可为空
     */
    private String noticeUrl;

    /**
     * 签名 - md5(businessId + userOrderId+ goodsId + goodsNum + orderIp + key)
     */
    private String sign;
}
