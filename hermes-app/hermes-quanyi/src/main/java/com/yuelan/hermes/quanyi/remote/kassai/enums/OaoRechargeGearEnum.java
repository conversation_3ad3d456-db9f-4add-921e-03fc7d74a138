package com.yuelan.hermes.quanyi.remote.kassai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OAO充值档位枚举
 *
 * <AUTHOR>
 * @date 2025/07/09
 */
@Getter
@AllArgsConstructor
public enum OaoRechargeGearEnum {

    /**
     * 无充值
     */
    NONE(0, "无"),

    /**
     * 50元档
     */
    FIFTY_YUAN(1, "50元档"),

    /**
     * 100元及以上档
     */
    HUNDRED_YUAN_AND_ABOVE(2, "100元及以上");

    private final Integer code;
    private final String description;

    /**
     * 根据档位码获取枚举
     *
     * @param code 档位码
     * @return 对应的枚举值
     */
    public static OaoRechargeGearEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OaoRechargeGearEnum gear : values()) {
            if (gear.getCode().equals(code)) {
                return gear;
            }
        }
        return null;
    }
}
