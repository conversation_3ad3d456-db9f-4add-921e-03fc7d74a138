package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;

@Data
public class MgFunNotifyReq {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 道具下发时间(13 位时间戳)
     */
    private Long sendTime;
    /**
     * 扩展字段(咪咕互娱透传用)
     */
    private String extrInfo;
    /**
     * 当前时间(13 位时间戳) bod
     */
    private Long timestamp;
    /**
     * 道具下发结果。
     * 1 下发成功
     * 0 下发失败
     */
    private String sendResult;
    /**
     * 道具发放结果描述。如道具发送失败，
     * 则建 议给出失败原因
     */
    private String sendMessage;
    /**
     * 道具兑换唯一识别码。
     */
    private String cdKey;
    /**
     * 是否进行sm4加密
     * 1 cdKey字段为sm4加密兑换码
     * 0 或 不填 cdKey字段为明文兑换码
     */
    private String cdKeyEncrypt;
}
