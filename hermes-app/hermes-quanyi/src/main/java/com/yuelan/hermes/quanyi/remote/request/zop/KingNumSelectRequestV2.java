package com.yuelan.hermes.quanyi.remote.request.zop;

import com.chinaunicom.zop.api.model.ZopRequest;
import com.chinaunicom.zop.api.model.response.KingNumSelectResponse;
import lombok.Data;

/**
 * <AUTHOR> 2024/4/29 下午4:36
 */
@Data
public class KingNumSelectRequestV2 implements ZopRequest<KingNumSelectResponse> {


    /**
     * 号码组id（必填），当qryType为02时必传
     */
    private String groupKey;

    /**
     * 省份编码（必填），如 北京：11
     */
    private String provinceCode;

    /**
     * 地市编码（必填），如 北京市110
     */
    private String cityCode;

    /**
     * 查询类型（必填）：01:列表查询（未分组号码查询） 02：组内号码查询
     */
    private String qryType;

    /**
     * 查询类型 1、普通选号 2、靓号选号 3、全部（普通、靓号都包括）
     */
    private String searchCategory;

    /**
     * 尾号查询 02: 匹配最后四位
     */
    private String searchType;

    /**
     * 尾号查询关键字：2-4位数字（和searchType配对出现，searchType有值的话，searchValue不可为空）
     */
    private String searchValue;

    /**
     * 预存款上限
     */
    private String advancePayTop;

    /**
     * 预存款下限
     */
    private String advancePayLower;

    /**
     * 号码类型,支持 AAAAA、AAAA、ABCDE、ABCD、AAA、AABB、ABAB、ABC、AA，从末尾匹配
     */
    private String codeTypeCode;

    /**
     * 网号 如:186
     */
    private String numNet;

    /**
     * 组号码不足时是否查询公共池号码 0:是 1：否
     */
    private String judgeType;

    /**
     * 特色靓号类型 01：爱情 02：吉祥 03：事业 04：全部
     */
    private String featureType;

    /**
     * 月承诺通信费
     */
    private String monthFeeLimit;

    /**
     * 生日靓号 传输月份: 01,02 ... 12 如01月,则显示尾号0101-0131,查询后4位
     */
    private String monthNum;

    /**
     * 协议期（月） 如果输入24个月，返回大于等于24个月协议期
     */
    private String monthLimit;

    /**
     * 是否查询靓号，值为1:返回靓号(包括特色靓号和靓号) ; 值为0:返回普号，该值为空时返回随机号码
     */
    private String niceTag;

    /**
     * 年代号码，见附录编码表
     */
    private String yearNum;

    /**
     * 支持10 ，100 默认为100
     */
    private String amounts;

    @Override
    public String getPath() {
        return "/link/num/select/v1";
    }

    @Override
    public String getPathName() {
        return "选号服务通用版本接口";
    }

    @Override
    public Class<KingNumSelectResponse> getResponseClass() {
        return KingNumSelectResponse.class;
    }


}
