package com.yuelan.hermes.quanyi.remote.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/11/18
 * @since 2024/11/18
 */
@Data
public class OrderChangeStatusReq {

    @Schema(description = "渠道订单号（唯一）")
    private String channelOrderNo;

    @Schema(description = "订单状态 1-激活 2-首冲 3-激活并且首冲 4-退单")
    private Integer status;

    @Schema(description = "激活时间")
    private LocalDateTime activeTime;

    @Schema(description = "首冲时间")
    private LocalDateTime firstChargeTime;

    @Schema(description = "首冲金额单位分")
    private Integer firstChargeAmount;

}
