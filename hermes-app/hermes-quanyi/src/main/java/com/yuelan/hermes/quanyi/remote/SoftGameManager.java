package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.common.pojo.properties.SoftGameProperties;
import com.yuelan.hermes.quanyi.common.util.SoftGameUtil;
import com.yuelan.hermes.quanyi.remote.softgame.request.SoftGameOrderQueryReq;
import com.yuelan.hermes.quanyi.remote.softgame.request.SoftGameOrderSubmitReq;
import com.yuelan.hermes.quanyi.remote.softgame.response.SoftGameBaseResponse;
import com.yuelan.hermes.quanyi.remote.softgame.response.SoftGameCardInfo;
import com.yuelan.hermes.quanyi.remote.softgame.response.SoftGameOrderQueryResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 软游通v2管理器
 *
 * <AUTHOR> 2025/7/31
 * @since 2025/7/31
 */
@Service
@Slf4j
@AllArgsConstructor
public class SoftGameManager {

    private final SoftGameProperties softGameProperties;

    /**
     * 订单提交
     *
     * @param req 订单提交请求
     * @return 统一响应格式
     */
    public SoftGameBaseResponse<String> submitOrder(SoftGameOrderSubmitReq req, StringBuilder reqBuild, StringBuilder respBuild) {
        if (!AppConstants.isReal()) {
            String goodsId = req.getGoodsId();
            // 测试环境切到沙箱环境
            softGameProperties.setSandbox(true);
            String testFailGoodsId = softGameProperties.getSandboxConfig().getTestFailGoodsId();
            String testSuccessGoodsId = softGameProperties.getSandboxConfig().getTestSuccessGoodsId();
            if (!testFailGoodsId.equals(goodsId) && !testSuccessGoodsId.equals(goodsId)) {
                throw new RuntimeException("测试环境只能使用测试商品");
            }
        }

        // 设置商户号和签名
        req.setBusinessId(softGameProperties.getCurrentBusinessId());

        // 生成签名
        String sign = SoftGameUtil.generateOrderSubmitSign(
                req.getBusinessId(),
                req.getUserOrderId(),
                req.getGoodsId(),
                req.getGoodsNum(),
                req.getOrderIp(),
                softGameProperties.getCurrentKey()
        );
        req.setSign(sign);

        reqBuild.append(JSON.toJSONString(req));

        String url = softGameProperties.getCurrentOrderSubmitUrl();
        log.info("软游通v2订单提交请求地址: {}", url);
        log.info("软游通v2订单提交请求参数: {}", JSON.toJSONString(req));

        try (HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .form(JSONObject.from(req))
                .execute()) {

            String body = response.body();
            respBuild.append(body);
            log.info("软游通v2订单提交返回参数: {}", body);
            return parseXmlResponse(body);
        }
    }

    /**
     * 订单查询
     *
     * @param userOrderId 商户订单号
     * @return 订单查询响应
     */
    public SoftGameBaseResponse<SoftGameOrderQueryResp> queryOrder(String userOrderId) {
        if (!AppConstants.isReal()) {
            // 测试环境切到沙箱环境
            softGameProperties.setSandbox(true);
        }
        SoftGameOrderQueryReq req = new SoftGameOrderQueryReq();
        req.setBusinessId(softGameProperties.getCurrentBusinessId());
        req.setUserOrderId(userOrderId);

        // 生成签名
        String sign = SoftGameUtil.generateOrderQuerySign(
                req.getBusinessId(),
                req.getUserOrderId(),
                softGameProperties.getCurrentKey()
        );
        req.setSign(sign);

        String url = softGameProperties.getCurrentOrderQueryUrl();
        log.info("软游通v2订单查询请求地址: {}", url);
        log.info("软游通v2订单查询请求参数: {}", JSON.toJSONString(req));

        try (HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .form(JSONObject.from(req))
                .execute()) {

            String body = response.body();
            log.info("软游通v2订单查询返回参数: {}", body);

            SoftGameOrderQueryResp queryResp = parseQueryXmlResponse(body);
            if (queryResp != null) {
                // 解密卡密信息
                if (CharSequenceUtil.isNotBlank(queryResp.getKmInfo())) {
                    List<SoftGameCardInfo> cardInfos = SoftGameUtil.decryptCardInfo(
                            queryResp.getKmInfo(),
                            softGameProperties.getKey()
                    );
                    log.info("软游通v2卡密解密结果: {}", JSON.toJSONString(cardInfos));
                }
                return SoftGameBaseResponse.success(queryResp);
            } else {
                return SoftGameBaseResponse.fail("02", "订单查询失败");
            }
        }
    }

    /**
     * 解析XML响应 - 通用方法
     */
    private SoftGameBaseResponse<String> parseXmlResponse(String xmlBody) {
        try {
            // 对于简单的响应，使用简单解析
            Map<String, String> xmlMap = SoftGameUtil.parseXmlToSimpleMap(xmlBody);
            return new SoftGameBaseResponse<String>() {{
                setResult(xmlMap.get("result"));
                setMes(xmlMap.get("mes"));
                setData(xmlBody);
            }};
        } catch (Exception e) {
            log.error("解析XML响应失败", e);
            return SoftGameBaseResponse.fail("02", "响应解析失败");
        }
    }

    /**
     * 解析查询XML响应 - 使用通用方法
     */
    private SoftGameOrderQueryResp parseQueryXmlResponse(String xmlBody) {
        try {
            // 对于复杂的响应，可以使用多层级解析
            Map<String, String> xmlMap = SoftGameUtil.parseXmlToSimpleMap(xmlBody);
            return SoftGameUtil.mapToObject(xmlMap, SoftGameOrderQueryResp.class);
        } catch (Exception e) {
            log.error("解析查询XML响应失败", e);
            return null;
        }
    }


}
