
package com.yuelan.hermes.quanyi.remote.kassai.response;

import lombok.Data;

/**
 * 卡赛接口通用返回结果.
 *
 * @param <T> a T object.
 * <AUTHOR>
 * @date 2025/07/09
 */
@Data
public class KsBaseResponse<T> {

    /**
     * 编码 200成功 非200表示接口失败.
     *
     * 下单接口：当code为119 时，需要以异步回调的状态为准，一般异步回调在20秒内会返回
     */
    private Integer code;

    /**
     * 数据.
     */
    private T data;

    /**
     * 消息.
     */
    private String message;
}
