package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.OrderMobileItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface OrderMobileItemMapper extends BaseMapper<OrderMobileItemDO> {
    OrderMobileItemDO findByItemNo(@Param("itemNo") String itemNo);

    List<OrderMobileItemDO> findByOrderNo(@Param("orderNo") String orderNo);

    List<OrderMobileItemDO> findByItemNoIn(@Param("list") Collection<String> list);

    int updateStatus(@Param("itemId") Long itemId, @Param("orderStatus") Integer orderStatus, @Param("extData") String extData);
}