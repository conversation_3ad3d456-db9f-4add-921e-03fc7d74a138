package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualDO;
import com.yuelan.hermes.quanyi.controller.request.GoodsVirtualListReq;
import com.yuelan.hermes.quanyi.controller.response.GoodsVirtualSearchRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GoodsVirtualMapper extends BaseMapper<GoodsVirtualDO> {
    int batchInsert(@Param("list") List<GoodsVirtualDO> list);

    int deleteById(@Param("id") Long id);

    Long countByGoodsVirtualListReq(GoodsVirtualListReq req);

    List<GoodsVirtualDO> pageByGoodsVirtualListReq(GoodsVirtualListReq req);

    int updateStatusById(@Param("id") Long id, @Param("status") Integer status);

    List<GoodsVirtualDO> findByGoodsNameLike(@Param("goodsName") String goodsName);

    GoodsVirtualDO findOne(Long id);

    List<GoodsVirtualSearchRsp> searchGoods(@Param("goodsId") Long goodsId, @Param("goodsName") String goodsName);
}