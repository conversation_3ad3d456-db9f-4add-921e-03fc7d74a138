package com.yuelan.hermes.quanyi.remote.hbsk.response;

import lombok.Data;

/**
 * 爱音乐回调响应
 *
 * <AUTHOR> 2025/7/17
 * @since 2025/7/17
 */
@Data
public class HuBeiShuKeCallbackResp {

    /**
     * 状态码：0000-成功
     */
    private String code;

    /**
     * 描述
     */
    private String description;

    public static HuBeiShuKeCallbackResp success() {
        HuBeiShuKeCallbackResp response = new HuBeiShuKeCallbackResp();
        response.setCode("0000");
        response.setDescription("成功");
        return response;
    }

    public static HuBeiShuKeCallbackResp error(String description) {
        HuBeiShuKeCallbackResp response = new HuBeiShuKeCallbackResp();
        response.setCode("9999");
        response.setDescription(description);
        return response;
    }
}
