package com.yuelan.hermes.quanyi.remote.softgame.request;

import lombok.Data;

/**
 * 软游通v2异步通知请求
 *
 * <AUTHOR> 2025/7/31
 * @since 2025/7/31
 */
@Data
public class SoftGameNotifyReq {

    /**
     * 商户号 - 由平台分配每个商户唯一的一个商户号
     */
    private String businessId;

    /**
     * 商户订单号 - 最大长度不超过32位的唯一流水号
     */
    private String userOrderId;

    /**
     * 充值凭证 - 字符串,不参与签名,且仅为话费类型订单的充值凭证
     */
    private String voucherNo;

    /**
     * 充值结果 - 01 成功 02 失败
     */
    private String status;

    /**
     * 结果说明 - 对充值的结果予以说明；特别是充值失败的时候需要对具体充值失败原因简单说明一下
     */
    private String mes;

    /**
     * 结算总金额 - 系统和进货平台结算金额
     */
    private String payoffPriceTotal;

    /**
     * 卡密信息 - kmInfo 为 3DES加密过的字符串
     */
    private String kmInfo;

    /**
     * 签名 - lcase(md5(businessId + userOrderId + status +密钥))
     */
    private String sign;

    public boolean isSuccess() {
        return "01".equals(status);
    }

    public boolean isFailed() {
        return "02".equals(status);
    }
}
