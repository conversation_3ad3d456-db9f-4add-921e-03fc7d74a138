package com.yuelan.hermes.quanyi.remote.request;

import lombok.Data;

@Data
public class SxfOrderCreateReq {
    /**
     * 商户编号
     */
    private String mno;
    /**
     * 商户订单号（字母、数字、下划线）
     * 需保证在合作方系统中不重复
     */
    private String ordNo;
    /**
     * 订单总金额(元)，格式：#########.##
     */
    private String amt;
    /**
     * 支付渠道，枚举值
     * 取值范围：
     * WECHAT 微信
     * ALIPAY 支付宝
     * UNIONPAY 银联
     */
    private String payType;
    /**
     * 支付方式，枚举值
     * 取值范围：
     * 02 微信公众号/支付宝生活号/银联js支付/支付宝小程序
     * 03 微信小程序
     */
    private String payWay;
    /**
     * 订单标题
     */
    private String subject;
    /**
     * 商户终端ip地址
     */
    private String trmIp;
    /**
     * 微信子公众号
     * 小程序必传，仅微信使用
     */
    private String subAppid;
    /**
     * 订单失效时间（单位分钟），格式：####
     * 取值范围：1-1440，默认5分钟
     */
    private String timeExpire;
    /**
     * 支付结果通知地址不上送则交易成功后，无异步交易结果通知
     */
    private String notifyUrl;
    /**
     * 扩展字段
     */
    private String extend;


}
