package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopPhonePoolDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2024/7/22 下午1:41
 */
public interface EccZopPhonePoolDOMapper extends BaseMapper<EccZopPhonePoolDO> {
    void saveBatchIgnore(@Param("list") List<EccZopPhonePoolDO> eccZopPhonePoolDOList);


    default Integer countBySpProdIdSpGoodIdAndNoUsed(Integer spProdId, String spGoodsId, String provinceCode, String cityCode) {
        return selectCount(Wrappers.<EccZopPhonePoolDO>lambdaQuery().eq(EccZopPhonePoolDO::getSpProdId, spProdId)
                .eq(EccZopPhonePoolDO::getSpGoodsId, spGoodsId)
                .eq(EccZopPhonePoolDO::getProvinceCode, provinceCode)
                .eq(EccZopPhonePoolDO::getCityCode, cityCode)
                .eq(EccZopPhonePoolDO::getUsed, 0)).intValue();
    }

    /**
     * 查询 spProdId 前N条id最大的
     */
    Long fetchMaxIdFromTopNRecords(@Param("spProdId") Integer spProdId, @Param("spGoodsId") String spGoodsId, @Param("provinceCode") String provinceCode, @Param("cityCode") String cityCode, @Param("topN") int topN);

    void deleteExceedPoolSize(@Param("spProdId") Integer spProdId, @Param("spGoodsId") String spGoodsId, @Param("provinceCode") String provinceCode, @Param("cityCode") String cityCode, @Param("minId") Long minId);

    List<EccZopPhonePoolDO> selectAvailablePhoneList(@Param("spProdId") Integer spProdId, @Param("spGoodsId") String spGoodsId, @Param("provinceCode") String provinceCode, @Param("cityCode") String cityCode, @Param("selectCount") int selectCont);

    default void deletePhone(String phone) {
        delete(Wrappers.<EccZopPhonePoolDO>lambdaQuery().eq(EccZopPhonePoolDO::getPhone, phone));
    }

    EccZopPhonePoolDO selectMinIdRecord(@Param("spProdId") Integer spProdId, @Param("spGoodsId") String zopGoodsId);
}