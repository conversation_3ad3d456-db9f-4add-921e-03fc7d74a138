package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitChannelDO;

/**
 * <AUTHOR> 2024/7/15 下午8:11
 */
public interface BenefitChannelDOMapper extends BaseMapper<BenefitChannelDO> {

    default BenefitChannelDO selectByChannelName(String channelName) {
        return selectOne(Wrappers.lambdaQuery(BenefitChannelDO.class)
                .eq(BenefitChannelDO::getChannelName, channelName));
    }
}