package com.yuelan.hermes.quanyi.remote.hbsk.response;

import lombok.Data;

/**
 * 活动商品信息
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class ActivityGood {

    /**
     * 商品ID
     */
    private Integer goodId;

    /**
     * 商品名称
     */
    private String goodName;

    /**
     * 商品描述
     */
    private String goodDesc;

    /**
     * 销售产品ID
     */
    private String saleProductId;

    /**
     * 销售产品名称
     */
    private String saleProductName;

    /**
     * 销售产品价格（分）
     */
    private String saleProductPrice;

    /**
     * 活动ID
     */
    private Integer activityId;

    /**
     * 排序
     */
    private Integer orderX;

    /**
     * 获取价格（元）
     */
    public Double getPriceInYuan() {
        if (saleProductPrice == null) {
            return null;
        }
        try {
            return Double.parseDouble(saleProductPrice) / 100.0;
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
