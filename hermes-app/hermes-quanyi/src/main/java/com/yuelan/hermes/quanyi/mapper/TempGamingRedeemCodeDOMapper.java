package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.TempGamingRedeemCodeDO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> 2024/7/29 下午8:34
 */
public interface TempGamingRedeemCodeDOMapper extends BaseMapper<TempGamingRedeemCodeDO> {
    int updateOneUsedByGoodsIdAndValidity(@Param("phone") String phone);

   default TempGamingRedeemCodeDO selectLastRedeemCode(String phone){
       return selectOne(Wrappers.lambdaQuery(TempGamingRedeemCodeDO.class)
               .eq(TempGamingRedeemCodeDO::getPhone,phone)
               .orderByDesc(TempGamingRedeemCodeDO::getUpdateTime)
               .last("limit 1"));

   }
}