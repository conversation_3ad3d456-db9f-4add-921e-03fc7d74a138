package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductItemDO;

import java.util.List;

/**
 * <AUTHOR> 2024/5/2 上午12:03
 */
public interface EccProductItemDOMapper extends MPJBaseMapper<EccProductItemDO> {

    /**
     * 查询正在使用的商品数量
     *
     * @param goodsId 商品id
     * @return 商品数量
     */
    default Integer getCountByGoodsId(Long goodsId) {
        return selectCount(Wrappers.lambdaQuery(EccProductItemDO.class)
                .eq(EccProductItemDO::getGoodsId, goodsId)).intValue();
    }

    /**
     * 查询prodId关联权益商品
     *
     * @param prodId 权益包id
     * @return 关联权益商品列表
     */
    default List<EccProductItemDO> listByProdId(Long prodId) {
        return selectList(Wrappers.lambdaQuery(EccProductItemDO.class)
                .eq(EccProductItemDO::getProdId, prodId));
    }
}