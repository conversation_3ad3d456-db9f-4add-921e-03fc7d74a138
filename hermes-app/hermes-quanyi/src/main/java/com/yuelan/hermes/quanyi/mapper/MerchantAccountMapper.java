package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.bo.MerchantAccountUpdateBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.MerchantAccountDO;
import com.yuelan.hermes.quanyi.controller.request.MchAccountListReq;
import com.yuelan.hermes.quanyi.controller.response.MchAccountListRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MerchantAccountMapper extends BaseMapper<MerchantAccountDO> {
    MerchantAccountDO findByMerchantId(@Param("merchantId") Long merchantId);

    /**
     * 抵扣
     */
    int deduct(MerchantAccountUpdateBO updateBO);

    /**
     * 退款
     */
    int refund(MerchantAccountUpdateBO updateBO);

    /**
     * 充值
     */
    int pay(MerchantAccountUpdateBO updateBO);

    List<MchAccountListRsp> pageByMchAccountListReq(MchAccountListReq req);

    Long countByMchAccountListReq(MchAccountListReq req);
}